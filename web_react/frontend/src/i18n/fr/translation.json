{"home": {"title": "Bienvenue sur Chez-TCFCA", "subtitle": "Votre plateforme complète de préparation au test TCF Canada avec des exercices interactifs et des conseils d'experts.", "loading": "Chargement des tests...", "errorTitle": "Erreur de chargement", "errorMessage": "Impossible de charger les tests. Veuillez réessayer plus tard.", "membershipWarningTitle": "Adhésion requise", "membershipWarningText": "Vous devez avoir un abonnement actif pour accéder à tous les tests.", "membershipWarningLink": "Voir les options d'abonnement", "startButton": "Commencer", "comingSoon": "Bientôt disponible", "ctaTitle": "Commencez votre préparation maintenant", "ctaSubtitle": "C<PERSON>ez un compte pour accéder aux tests gratuits et suivre vos progrès.", "registerButton": "S'inscrire gratuitement", "loginButton": "Se connecter", "hero": {"title": "Chez-TCFCA"}, "whyChooseUs": {"title": "Pourquoi choisir notre plateforme ?", "subtitle": "Six avantages principaux", "description": "Six avantages principaux pour vous aider à préparer efficacement le TCF Canada", "subdescription": "Fonctionnalités complètes conçues pour des résultats d'apprentissage optimaux"}, "testSections": {"title": "Commencez votre parcours de préparation", "subtitle": "<PERSON><PERSON><PERSON><PERSON> votre pratique", "description": "Choisissez le module de test que vous souhaitez pratiquer et commencez une préparation efficace"}, "welcome": {"title": "Bienvenue dans votre parcours de réussite TCF Canada", "subtitle": "Maîtrisez l'examen TCF Canada avec notre plateforme interactive complète conçue pour un apprentissage et une réussite optimaux.", "description": "TCF Canada fournit les outils de préparation les plus efficaces avec de vraies questions d'examen, des explications détaillées et un suivi de progrès personnalisé. Rejoignez des milliers de candidats qui ont réussi leurs objectifs d'immigration avec notre plateforme."}, "features": {"title": "Pourquoi choisir <PERSON>ez-TCFCA ?", "subtitle": "Découvrez les fonctionnalités puissantes qui font de notre plateforme le choix n°1 pour la préparation TCF Canada", "comprehensive": {"title": "Banque de Questions Dédupliquées et Classifiées", "description": "Accédez à des milliers de questions authentiques TCF Canada avec un système avancé de déduplication et de classification pour un apprentissage efficace et ciblé du contenu à haute valeur."}, "enhanced": {"title": "Qualité Audio Améliorée", "description": "Tous les audios de faible qualité ont été améliorés pour garantir une expérience de pratique optimale."}, "verified": {"title": "Réponses Vérifiées", "description": "Toutes les réponses ont été soigneusement examinées et vérifiées pour leur exactitude et leur fiabilité."}, "explanations": {"title": "Explications Détail<PERSON>", "description": "Chaque question est accompagnée d'explications détaillées pour vous aider à comprendre les concepts et les stratégies."}, "customizable": {"title": "Écoute Intensive", "description": "Support pour la pratique d'écoute intensive. Toutes les questions d'écoute ont été segmentées par phrase - cliquez sur n'importe quelle phrase pour jouer cette partie spécifique."}, "mockExams": {"title": "Examens Simulés", "description": "Pratiquez avec des examens simulés chronométrés réalistes pour développer votre confiance et vos compétences en gestion du temps."}, "interactive": {"title": "Outils d'apprentissage interactifs", "description": "Surlignez du texte, prenez des notes, marquez des questions et suivez vos progrès avec nos fonctionnalités d'apprentissage avancées."}, "expert": {"title": "Contenu vérifié par des experts", "description": "Toutes les questions et réponses sont vérifiées par des experts linguistiques, avec des questions controversées clairement signalées pour la transparence."}, "personalized": {"title": "Expérience d'apprentissage personnalisée", "description": "Parcours d'apprentissage adaptatifs, analyses détaillées et sessions de pratique personnalisées basées sur vos performances et objectifs."}, "realistic": {"title": "Examens blancs réalistes", "description": "Pratiquez avec des examens blancs chronométrés qui simulent les conditions d'examen réelles, vous aidant à développer votre confiance et vos compétences de gestion du temps."}, "analytics": {"title": "Analy<PERSON> de progrès d<PERSON>", "description": "Suivez vos performances avec des statistiques complètes, identifiez les domaines faibles et surveillez votre amélioration au fil du temps."}}, "guide": {"title": "À propos de Chez-TCFCA", "description1": "Cette plateforme est spécialement conçue pour la préparation au TCF Canada. Avec notre base de données complète de questions, vous rencontrerez la plupart des questions qui apparaissent dans l'examen réel, ce qui en fait la méthode de préparation la plus efficace si vous vous engagez à compléter tous les tests disponibles.", "description2": "Notre plateforme fournit des outils et ressources complets pour soutenir votre parcours de préparation à l'examen.", "contactNote": "Nous vous invitons à utiliser \"Contacter le support\" pour partager vos commentaires ou signaler tout problème."}, "announcements": {"title": "Dernières mises à jour et opportunités", "description": "Restez informé de nos dernières fonctionnalités, offres et moyens de contribuer", "promoCode": {"title": "Offre à durée limitée", "message": "Obtenez 3 jours d'accès premium avec le code promo <strong>TCFCA</strong> ! Parfait pour tester toutes nos fonctionnalités avancées avant de vous engager dans un abonnement complet.<br/><br/><strong>Important :</strong> Seulement <strong>30 codes disponibles par jour</strong>, renouvelés chaque jour à minuit. Chaque compte ne peut utiliser le code promo qu'<strong>une seule fois</strong>. Ne ratez pas cette occasion — réclamez le vôtre aujourd'hui !", "actionText": "Utiliser le code promo"}, "bugReport": {"title": "Aidez-nous à nous améliorer", "message": "<strong>1.</strong> <PERSON>ur la <strong>première erreur/bug que vous découvrez et signalez</strong> qui est confirmée, vous recevrez un <strong>code promo de 7 jours d'adhésion</strong> (partageable avec d'autres, utilisable jusqu'à 3 fois).<br/><br/><strong>2.</strong> Pour <strong>chaque erreur/bug confirmée suivante</strong> que vous signalez, vous obtiendrez <strong>3 jours supplémentaires</strong> d'adhésion.<br/><br/><strong>Exception :</strong> Pour les erreurs d'hallucination de texte d'écoute et de lecture (comme \"merci\" à la fin ou des phrases répétées), vous recevrez <strong>1 jour</strong> d'adhésion.", "actionText": "Signaler un bug"}, "downloads": {"title": "Téléchargez des matériaux d'étude gratuits", "message": "Accédez à notre collection complète de matériaux de préparation TCF Canada gratuits, incluant des tests d'entraînement, des guides d'étude et des documents de référence.<br/><br/><strong>Contenu vedette :</strong> Matériaux d'<strong>expression écrite complets avec exemples de corrections détaillées</strong> et matériaux d'<strong>expression orale complets avec échantillons de corrections d'experts</strong>. Parfait pour comprendre les bonnes techniques et éviter les erreurs courantes.", "actionText": "Parcourir les téléchargements gratuits", "loginText": "Connexion pour téléchargements gratuits"}}, "sections": {"title": "Commencez votre pratique", "listening": {"title": "Compréhension orale", "description": "<PERSON><PERSON><PERSON><PERSON>z le français parlé avec 39 questions à choix multiples, une qualité audio améliorée et une révision de transcription phrase par phrase."}, "reading": {"title": "Compréhension écrite", "description": "Excellez en compréhension écrite française avec des outils de surlignage, des fonctionnalités de prise de notes et des niveaux de difficulté progressifs."}, "writing": {"title": "Expression écrite", "description": "Perfectionnez vos compétences en rédaction française avec des tâches structurées, des corrections d'experts et des commentaires complets."}, "speaking": {"title": "Expression orale", "description": "Développez votre confiance en français parlé avec des scénarios réalistes, des exemples de réponses et des sessions de pratique structurées."}}}, "navigation": {"title": "Navigation des Questions", "answered": "Répondu", "wrongAnswer": "Mauvaise réponse", "notAnswered": "Non répondu", "pointsEach": "pts chacun", "hint": {"moveMouseToTop": "Déplacez la souris vers le haut pour afficher la navigation"}}, "layout": {"logout": "Déconnexion", "login": "Connexion", "register": "<PERSON><PERSON><PERSON> un compte", "home": "Accueil", "profile": "Profil", "collection": "Ma Collection", "notebook": "Carnet", "membership": {"becomeMember": "<PERSON><PERSON>r membre", "extendMembership": "Prolonger l'abonnement"}, "support": "Support", "contactSupport": "<PERSON>er le support", "quickNavigation": {"title": "Sections", "listening": "Écoute", "reading": "Lecture", "writing": "Écriture", "speaking": "Expression orale"}, "tools": {"title": "Outils", "downloadMaterials": "Télécharger des Matériaux"}, "footer": {"copyright": "Tous droits réservés.", "contactSupport": "<PERSON>er le support"}}, "theme": {"switchToDark": "Passer au mode sombre", "switchToLight": "Passer au mode clair"}, "common": {"loading": "Chargement...", "pleaseWait": "<PERSON><PERSON><PERSON><PERSON>er", "error": "<PERSON><PERSON><PERSON>", "success": "Su<PERSON>ès", "cancel": "Annuler", "confirm": "Confirmer", "save": "Enregistrer", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "close": "<PERSON><PERSON><PERSON>", "back": "Retour", "continue": "<PERSON><PERSON><PERSON>", "previous": "Précédent", "next": "Suivant", "start": "Commencer", "finish": "<PERSON><PERSON><PERSON>", "submit": "So<PERSON><PERSON><PERSON>", "retry": "<PERSON><PERSON><PERSON><PERSON>", "refresh": "Actualiser", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "sort": "<PERSON><PERSON>", "view": "Voir", "hide": "Masquer", "show": "<PERSON><PERSON><PERSON><PERSON>", "expand": "Développer", "collapse": "<PERSON><PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unselect": "Désé<PERSON><PERSON>ner", "copy": "<PERSON><PERSON><PERSON>", "paste": "<PERSON><PERSON>", "cut": "Couper", "undo": "Annuler", "redo": "<PERSON><PERSON><PERSON>", "free": "<PERSON><PERSON><PERSON>", "premium": "Premium", "required": "Requis", "optional": "Optionnel", "yes": "O<PERSON>", "no": "Non", "enabled": "Activé", "disabled": "Désactivé", "active": "Actif", "inactive": "Inactif", "available": "Disponible", "unavailable": "Indisponible", "online": "En ligne", "offline": "<PERSON><PERSON> ligne", "connected": "Connecté", "disconnected": "Déconnecté", "public": "Public", "private": "Priv<PERSON>", "new": "Nouveau", "old": "Ancien", "updated": "Mis à jour", "created": "<PERSON><PERSON><PERSON>", "modified": "<PERSON><PERSON><PERSON><PERSON>", "deleted": "Supprimé", "and": "et", "highlight": "<PERSON><PERSON><PERSON><PERSON>", "removeHighlight": "Cliquez pour supprimer ce surlignage", "clickToRemove": "Cliquez pour supprimer", "autoPlay": "Lecture automatique", "speed": "Vitesse", "audioWaitTime": "Temps d'attente avant lecture auto", "waitTime": {"0": "<PERSON><PERSON><PERSON>", "5": "5 secondes", "10": "10 secondes", "15": "15 secondes", "20": "20 secondes", "25": "25 secondes", "30": "30 secondes"}, "bookmarks": {"add": "Ajouter à la collection", "remove": "<PERSON><PERSON><PERSON> de la collection", "added": {"title": "Question sauvegardée", "message": "La question a été ajoutée à votre collection"}, "removed": {"title": "Marque-page supprimé", "message": "La question a été retirée de votre collection"}, "errors": {"loginRequired": {"title": "Connexion requise", "message": "Vous devez être connecté pour sauvegarder des questions"}, "alreadySaved": {"title": "<PERSON><PERSON><PERSON><PERSON> sa<PERSON>", "message": "Cette question est déjà dans votre collection"}, "saveFailed": {"title": "<PERSON><PERSON><PERSON>", "message": "Impossible de sauvegarder la question. Veuillez réessayer."}}}}, "loadingStates": {"application": "Chargement de l'application...", "initializing": "Initialisation...", "tests": "Chargement des tests...", "testProgress": "Chargement du progrès des tests...", "speaking": "Chargement des données d'expression orale...", "questions": "Chargement des questions...", "data": "Chargement des données...", "profile": "Chargement du profil...", "membership": "Chargement de l'abonnement...", "collection": "Chargement de votre collection...", "history": "Chargement de l'historique...", "exam": "Chargement de l'examen...", "mockExam": "Chargement de l'examen blanc...", "month": "Chargement du mois...", "writing": "Chargement des exercices d'écriture...", "audio": "Chargement de l'audio...", "image": "Chargement de l'image...", "saving": "Enregistrement...", "submitting": "Soumission...", "processing": "Traitement...", "uploading": "Téléchargement...", "downloading": "Téléchargement...", "connecting": "Connexion en cours...", "authenticating": "Authentification...", "verifying": "Vérification...", "generating": "Génération...", "calculating": "Calcul..."}, "profile": {"title": "Mon Profil", "accountInfo": "Informations du compte", "username": "Nom d'utilisateur", "email": "Email", "joinedDate": "Date d'inscription", "membershipStatus": "Statut d'abonnement", "statusLabel": "Statut", "type": "Type", "expiresOn": "Expire le", "lifetimeAccess": "Abonnement à vie - Accès illimité", "freeAccount": "<PERSON><PERSON>e gratuit", "premiumMember": "Membre Premium", "expired": "Expiré", "manageSubscription": "<PERSON><PERSON>rer l'abonnement", "subscribe": "<PERSON>'abonner", "promoCode": "Code promo", "testHistory": "Historique des tests", "noHistory": "Aucun historique de test disponible", "startTesting": "Commencer un test pour voir votre progression ici", "listView": "<PERSON><PERSON> liste", "gridView": "Vue grille", "deleteHistory": "Supprimer l'historique", "results": "Résultats", "review": "Revoir", "notStarted": "Non commencé", "completed": "<PERSON><PERSON><PERSON><PERSON>", "completedAndGraded": "Term<PERSON><PERSON> et noté", "progress": "Progression", "confirmDelete": "Confirmer la <PERSON>", "deleteHistoryWarning": "Êtes-vous sûr de vouloir supprimer l'historique de ce test ? Cette action est irréversible.", "currentResults": "Résultats actuels", "correct": "correctes", "wrong": "incorrectes", "answers": "réponses", "historyDeleted": "Historique supprimé avec succès", "deleteError": "Erreur lors de la suppression de l'historique", "viewHistoryPage": "Voir la page historique complète", "quickActions": "Actions rapides", "takeTest": "Faire un test", "myNotes": "Mes notes", "fullHistory": "Historique complet", "deleteTestHistory": "Supprimer l'historique du test", "deleteWarning": "Êtes-vous sûr de vouloir supprimer l'historique de ce test ? Cette action est irréversible et vous devrez recommencer le test depuis le début.", "testLabel": "Test", "currentScore": "Score actuel", "cancel": "Annuler", "deleteHistoryButton": "Supprimer l'historique", "noTestsYet": "Aucun test effectué pour le moment", "takeFirstTest": "Faire mon premier test", "resetFilters": "Réinitialiser les filtres", "noTestsFound": "Aucun test trouvé avec ces filtres", "sections": {"listening": "Compréhension Orale", "reading": "Compréhension Écrite", "writing": "Expression Écrite", "speaking": "Expression Orale"}, "status": {"lifetime": "À vie", "active": "Actif", "expired": "Expiré", "free": "<PERSON><PERSON><PERSON>"}, "buttons": {"continue": "<PERSON><PERSON><PERSON>", "start": "Commencer", "results": "Résultats", "review": "Revoir"}, "progressText": {"completed": "% complété", "daysLeft": "jour", "daysLeftPlural": "jours", "hoursLeft": "heure", "hoursLeftPlural": "heures", "minutesLeft": "minute", "minutesLeftPlural": "minutes", "remaining": "restant"}, "passwordChange": {"title": "Changer le mot de passe", "button": "Changer le mot de passe", "emailSent": "<PERSON>ail envoy<PERSON>", "linkSent": "Un lien de réinitialisation a été envoyé à", "checkEmail": "Vérifiez votre email pour le lien de réinitialisation", "sendError": "Erreur lors de l'envoi de l'email", "close": "<PERSON><PERSON><PERSON>", "securityNote": "Pour votre sécurité, nous vous enverrons un lien de réinitialisation par email pour changer votre mot de passe.", "emailAccount": "<PERSON><PERSON> de votre compte", "whyMethod": "Pourquoi cette méthode?", "securityExplanation": "L'envoi d'un email de vérification garantit que vous êtes bien le propriétaire du compte et renforce la sécurité.", "cancel": "Annuler", "sendLink": "Envoyer le lien", "sending": "Envoi en cours...", "successSent": "Email envoyé avec succès !", "resetSent": "Un lien de réinitialisation sécurisé a été envoyé à", "checkInbox": "Vérifiez votre boîte de réception et cliquez sur le lien pour créer un nouveau mot de passe.", "linkExpires": "Le lien expirera dans 1 heure pour votre sécurité.", "troubleshoot": "Vous ne voyez pas l'email?", "checkSpam": "• Vérifiez votre dossier spam/courrier indésirable", "waitTime": "• Attendez quelques minutes, la livraison peut prendre du temps", "retryLater": "• Vous pouvez fermer cette fenêtre et réessayer plus tard"}, "tools": "Outils", "translationMethod": "Méthode de traduction", "gpt4Description": "GPT-4 : Traductions de meilleure qualité, réponse plus lente (peut ne pas fonctionner dans les zones restreintes)", "azureDescription": "Azure : Traductions plus rapides, qualité standard (par défaut, fonctionne mondialement)", "profileUpdated": "Profil mis à jour", "translationMethodUpdated": "Préférence de méthode de traduction sauvegardée avec succès", "updateError": "Échec de la mise à jour du profil", "logout": {"confirm": "Voulez-vous vraiment vous déconnecter ?", "button": "Se déconnecter", "success": "Déconnexion réussie", "error": "Erreur lors de la déconnexion"}, "filters": {"all": "Tous", "sections": {"all": "Toutes", "listening": "Écoute", "reading": "Lecture", "writing": "Écriture", "speaking": "Oral"}, "status": {"all": "Tous", "completed": "Te<PERSON>in<PERSON>", "inProgress": "En cours", "free": "Gratuits", "premium": "Premium"}, "sortBy": {"dateDesc": "Date (récent)", "dateAsc": "Date (ancien)", "scoreDesc": "Score (élevé)", "scoreAsc": "Score (faible)", "section": "Section", "testId": "Numéro de <PERSON>"}, "placeholders": {"search": "Rechercher un test...", "section": "Section", "status": "Statut", "sortBy": "Trier par", "perPage": "Par page"}}, "unauthorized": {"title": "Accès non autorisé", "message": "Vous devez être connecté pour accéder à cette page."}}, "membership": {"title": "Abonnements Premium", "subtitle": "Accédez à tous les tests TCF Canada et maximisez vos chances de réussite", "currentStatus": "Statut actuel", "lifetimeSubscription": "Abonnement à vie", "activeSubscription": "Abonnement actif", "expiredSubscription": "Abonnement expiré", "freeAccount": "<PERSON><PERSON>e gratuit", "lifetimeMessage": "Vous avez un abonnement à vie ! Vous avez accès à toutes les fonctionnalités premium de façon permanente.", "activeMessage": "Votre abonnement premium expire le", "expiredMessage": "Votre abonnement a expiré. Renouvelez pour continuer à accéder à tous les tests.", "freeMessage": "Vous utilisez actuellement la version gratuite. Passez au premium pour un accès complet.", "featuresTitle": "Que comprend l'abonnement Premium ?", "choosePlan": "Choisissez votre plan", "mostPopular": "Plus populaire", "duration": {"month": "mois", "year": "an", "lifetime": "à vie"}, "buttons": {"subscribe": "<PERSON>'abonner", "becomeMember": "<PERSON><PERSON>r membre", "changePlan": "Changer de plan", "renew": "Renouveler", "upgradeToLifetime": "Passer à vie"}, "lifetimeThankYou": {"title": "Merci pour votre confiance !", "message": "Votre abonnement à vie vous donne accès à toutes les fonctionnalités premium de façon permanente.", "startTests": "Commencer les tests", "viewProfile": "Voir mon profil"}, "promoSection": {"title": "Vous avez un code promotionnel ?", "description": "Utilisez votre code promo pour obtenir un accès gratuit à la plateforme", "button": "Utiliser un code promo"}, "freeAccountFeatures": {"title": "Avec le compte gratuit", "list": ["Accès aux tests gratuits", "Création de notes personnelles", "Suivi de base des progrès", "Support communautaire"], "description": "Parfait pour découvrir la plateforme et commencer votre préparation !"}, "premiumFeatures": {"unlimited": "Tests illimités", "mockExams": "Examens simulés", "allLevels": "<PERSON><PERSON> les niveaux (A1-C2)", "detailedResults": "Résultats détaillés", "progressTracking": "<PERSON><PERSON><PERSON> de <PERSON> avan<PERSON>", "prioritySupport": "Support prioritaire"}, "loginRequired": "Vous devez vous connecter pour vous abonner", "features": {"readingComprehension": {"title": "Compréhension Écrite", "description": "<PERSON><PERSON><PERSON><PERSON>z les compétences de lecture avec des textes variés et des niveaux de difficulté"}, "listeningComprehension": {"title": "Compréhension Orale", "description": "Améliorez vos compétences d'écoute avec des matériaux audio authentiques"}, "writtenExpression": {"title": "Expression Écrite", "description": "Développez vos compétences d'écriture avec des exercices guidés et des commentaires"}, "oralExpression": {"title": "Expression Orale", "description": "Pratiquez l'expression orale avec des exercices interactifs et des exemples"}, "accessAllTests": "Accès à tous les tests", "progressTracking": "Suivi des progrès", "detailedCorrections": "Corrections détaillées", "prioritySupport": "Support prioritaire", "mockExams": "Examens blancs", "noRenewal": "Pas de renouvellement nécessaire", "exclusiveAccess": "Accès exclusif aux nouveaux tests"}, "plans": {"week": {"name": "Hebdomadaire", "duration": "semaine"}, "monthly": {"name": "<PERSON><PERSON><PERSON>", "duration": "mois"}, "quarterly": {"name": "<PERSON><PERSON><PERSON><PERSON>", "duration": "3 mois"}, "yearly": {"name": "1 An", "duration": "12 mois"}, "lifetime": {"name": "À vie", "duration": "à vie"}}, "contact": {"text": "Des questions sur nos abonnements ?", "link": "Contactez-nous"}, "errors": {"stripeNotConfigured": "Erreur de configuration", "stripeKeyMissing": "Clé Stripe non configurée", "paymentSessionError": "Erreur lors de la création de la session de paiement", "paymentError": "<PERSON><PERSON><PERSON>", "loginRequiredTitle": "Connexion requise", "loginRequiredMessage": "Vous devez vous connecter pour vous abonner"}}, "auth": {"logout": {"success": "Vous avez été déconnecté avec succès", "error": "Erreur lors de la déconnexion"}, "session": {"invalidated": {"title": "<PERSON><PERSON><PERSON> de Sécurité du Compte", "deviceAccess": "Votre compte a été accédé depuis un autre appareil.", "deviceAccessWithTime": "Votre compte a été accédé depuis un autre appareil à {{time}}.", "loggedOut": "Pour votre sécurité, vous avez été déconnecté.", "redirecting": "Redirection vers la page de connexion dans {{seconds}} secondes...", "clickToLogin": "Cliquez sur cette notification pour aller à la connexion maintenant.", "loginSuccess": "Connexion Réussie", "loginSuccessMessage": "Bon retour ! Votre session est maintenant active.", "logoutSuccess": "Déconnecté", "logoutSuccessMessage": "Vous avez été déconnecté avec succès."}}, "login": {"title": "Connexion", "noAccount": "Vous n'avez pas de compte ?", "createAccount": "<PERSON><PERSON><PERSON> un compte", "continueWithGoogle": "Continuer avec Google", "orDivider": "ou", "usernameEmail": "Nom d'utilisateur ou Email", "usernamePlaceholder": "<NAME_EMAIL>", "password": "Mot de passe", "passwordPlaceholder": "Votre mot de passe", "forgotPassword": "Mot de passe oublié ?", "signIn": "Se connecter", "errors": {"usernameRequired": "Nom d'utilisateur ou email requis", "passwordMinLength": "Le mot de passe doit contenir au moins 6 caractères", "invalidCredentials": "Nom d'utilisateur/email ou mot de passe incorrect", "emailNotVerified": "Email non vérifié", "verifyEmailMessage": "Veuillez vérifier votre email avant de vous connecter.", "googleSignInFailed": "Connexion avec Google échouée. Veuillez réessayer."}}, "register": {"title": "<PERSON><PERSON><PERSON> votre compte", "username": "Nom d'utilisateur", "usernamePlaceholder": "Entrez votre nom d'utilisateur", "email": "Email", "emailPlaceholder": "Entrez votre email", "password": "Mot de passe", "passwordPlaceholder": "Entrez votre mot de passe", "confirmPassword": "Confirmer le mot de passe", "confirmPasswordPlaceholder": "Confirmez votre mot de passe", "createAccount": "<PERSON><PERSON><PERSON> un compte", "updateRegistration": "Mettre à jour l'inscription", "haveAccount": "Vous avez déjà un compte ?", "signIn": "Se connecter", "continueWithGoogle": "Continuer avec Google", "orDivider": "Ou continuer avec email", "fromVerification": "V<PERSON> pouvez mettre à jour vos détails d'inscription ci-dessous ou continuer avec la vérification existante.", "pendingExists": "Vous avez déjà une inscription en attente pour {{email}}.", "goToVerification": "Aller à la vérification", "success": {"title": "Inscription réussie", "created": "Veuillez vérifier votre email pour le code de vérification.", "updated": "Inscription mise à jour. Veuillez vérifier votre email pour le nouveau code de vérification."}, "errors": {"usernameMinLength": "Le nom d'utilisateur doit contenir au moins 3 caractères", "emailInvalid": "<PERSON><PERSON><PERSON> email invalide", "passwordMinLength": "Le mot de passe doit contenir au moins 6 caractères", "passwordsDontMatch": "Les mots de passe ne correspondent pas", "googleSignInFailed": "Connexion Google échouée. Veuillez réessayer.", "usernameConflict": "Nom d'utilisateur déjà pris", "usernameConflictMessage": "Ce nom d'utilisateur est déjà utilisé par une autre inscription en attente. Veuillez choisir un nom d'utilisateur différent."}}, "forgotPassword": {"title": "Réinitialiser le mot de passe", "description": "Récupération de mot de passe", "instructions": "Entrez votre adresse email et nous vous enverrons un lien sécurisé pour réinitialiser votre mot de passe.", "emailLabel": "<PERSON><PERSON><PERSON> email", "emailPlaceholder": "<EMAIL>", "tips": {"title": "Conseils utiles", "spam": "• Vérifiez votre dossier spam si vous ne recevez pas l'email", "expires": "• Le lien sera valide pendant 1 heure", "correctEmail": "• Assurez-vous d'utiliser l'email de votre compte"}, "cancel": "Annuler", "sendLink": "Envoyer le lien", "sending": "Envoi en cours...", "success": {"title": "<PERSON>ail envoy<PERSON>", "message": "Si l'email existe, un lien de réinitialisation a été envoyé."}, "error": {"title": "<PERSON><PERSON><PERSON>", "message": "Erreur lors de l'envoi de l'email. Veuillez réessayer."}}, "resetPassword": {"title": "<PERSON><PERSON>er un nouveau mot de passe", "newPassword": "Nouveau mot de passe", "confirmPassword": "Confirmer le nouveau mot de passe", "updatePassword": "Mettre à jour le mot de passe", "updating": "Mise à jour...", "success": {"title": "Mot de passe mis à jour", "message": "Votre mot de passe a été mis à jour avec succès."}, "error": {"title": "<PERSON><PERSON><PERSON>", "message": "Erreur lors de la mise à jour du mot de passe. Le lien a peut-être expiré."}, "errors": {"passwordMinLength": "Le mot de passe doit contenir au moins 6 caractères", "passwordsDontMatch": "Les mots de passe ne correspondent pas"}}}, "contact": {"title": "<PERSON>er le support", "yourInfo": "Vos informations (automatiquement incluses)", "describeProblem": "Décrivez votre problème", "placeholder": "Décrivez votre problème en détail... Plus vous donnez d'informations, plus nous pourrons vous aider efficacement.", "tips": {"title": "Conseils pour un support efficace", "step1": "Décrivez les étapes qui ont mené au problème", "step2": "<PERSON><PERSON><PERSON> le navigateur que vous utilisez", "step3": "<PERSON><PERSON>ez des captures d'écran si nécessaire", "step4": "Nous vous répondrons dans les 24-48 heures par email"}, "cancel": "Annuler", "send": "Envoyer le message", "sending": "Envoi en cours...", "success": {"title": "Message envoyé", "message": "Votre message a été envoyé avec succès. Nous vous répondrons dans les 24-48 heures."}, "error": {"title": "<PERSON><PERSON><PERSON>", "message": "Erreur lors de l'envoi du message. Veuillez réessayer."}, "validation": {"minLength": "Le message doit contenir au moins 10 caractères"}}, "collection": {"title": "Ma Collection", "questionsCount_one": "{{count}} question sa<PERSON><PERSON><PERSON>", "questionsCount_other": "{{count}} questions sauve<PERSON><PERSON>", "empty": {"title": "Votre collection est vide", "message": "Commencez à sauvegarder des questions en cliquant sur l'icône étoile lors de vos tests de lecture et d'écoute.", "startReading": "Commencer un test de lecture"}, "search": "Rechercher par test ou numéro de question...", "sections": {"reading": "LECTURE", "listening": "ÉCOUTE"}, "practice": {"checkAnswer": "Vérifier la réponse", "tryAgain": "<PERSON><PERSON><PERSON><PERSON>", "selectQuestion": "Sélectionnez une question ci-dessus pour commencer à pratiquer", "showTranscript": "Afficher le texte original", "hideTranscript": "Masquer le texte original", "showAllText": "A<PERSON><PERSON><PERSON> tout le texte", "hideAllText": "Masquer tout le texte"}, "notifications": {"removed": {"title": "Question supprimée", "message": "La question a été supprimée de votre collection"}, "selectAnswer": {"title": "Avertissement", "message": "Veuillez sélectionner une réponse avant de vérifier."}}, "errors": {"loginRequired": "Vous devez être connecté pour accéder à votre collection.", "loadingError": "Erreur lors du chargement de votre collection. Veuillez réessayer.", "noData": "Données de question non disponibles pour la question"}, "audio": {"error": {"title": "Erreur Audio"}, "premiumRequired": {"title": "Abonnement Premium Requis", "message": "Cet audio provient de contenu premium. Vous avez besoin d'un abonnement actif pour accéder aux fichiers audio premium.", "action": "Passez au premium pour accéder à tout le contenu audio de votre collection."}, "membershipExpired": {"title": "Abonnement Expiré", "message": "Votre abonnement premium a expiré. Vous ne pouvez plus accéder au contenu audio premium.", "action": "Renouvelez votre abonnement pour continuer à accéder aux fichiers audio premium."}, "upgradeButton": "Passer au Premium"}}, "mockExam": {"title": "Examen Blanc", "subtitle": {"listening": "Compréhension Orale", "reading": "Compréhension Écrite", "writing": "Expression Écrite", "speaking": "Expression Orale"}, "backToTests": "Retour aux Tests", "finishExam": "Terminer l'Examen Blanc", "loading": "Chargement de l'examen blanc...", "errors": {"loadFailed": "Impossible de charger l'examen blanc.", "dataNotFound": "Données non trouvées", "tryAgain": "Veuillez réessayer.", "noQuestions": "Aucune question trouvée pour cet examen blanc.", "questionNotFound": "Question {{number}} non trouvée.", "returnToFirst": "Retourner à la question 1"}, "results": {"completed": "Examen blanc terminé !", "score": "Vous avez obtenu {{score}} points sur {{total}} questions.", "tcfScore": "Score TCF (sur 699) : {{score}} / 699 ({{percent}}%)", "correctAnswers": "Bonnes réponses : {{count}}", "incorrectQuestions": "Questions incorrectes :", "question": "Q{{number}}", "yourAnswer": "Votre réponse : {{answer}}", "correctAnswer": "Bonne réponse : {{answer}}", "submitError": "Erreur lors de la soumission de l'examen blanc."}}, "test": {"sections": {"listening": "Compréhension Orale", "reading": "Compréhension Écrite", "writing": "Expression Écrite", "speaking": "Expression Orale"}, "free": "<PERSON><PERSON><PERSON>", "submit": "So<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON><PERSON>", "question": "Question", "of": "de", "loading": {"totalQuestions": "Questions totales :", "currentQuestion": "Question actuelle :", "test": "Test", "section": "Section :", "returnToQuestion": "Retourner à la question", "data": "Données :", "urlStructure": "Structure d'URL attendue :", "forTests": "Pour les tests :", "forLevels": "Pour les niveaux :", "returnHome": "Retour à l'accueil", "viewTests": "Voir les tests", "practiceByLevel": "Pratique par Niveau", "oralComprehension": "Compréhension Orale", "writtenComprehension": "Compréhension Écrite", "writtenExpression": "Expression Écrite", "oralExpression": "Expression Orale", "mockExam": "Examen Blanc", "returnTests": "Retour aux Tests", "questionNavigation": "Navigation des Questions", "answered": "Répondu", "wrongAnswer": "Mauvaise réponse", "unanswered": "Non répondu", "question": "Question", "appearedIn": "<PERSON><PERSON><PERSON> dans :", "browserNotSupported": "Votre navigateur ne supporte pas l'élément audio.", "hideOriginalText": "Masquer le texte original", "showOriginalText": "Afficher le texte original", "hideAllText": "Masquer tout le texte", "showAllText": "A<PERSON><PERSON><PERSON> tout le texte", "previous": "Précédent", "next": "Suivant", "submitTest": "Soumettre le test"}, "error": "Erreur de chargement", "mockExam": "Examen Blanc", "groupTest": "Test par Niveau", "graded": {"title": "Test déjà noté", "correct": "r<PERSON><PERSON><PERSON><PERSON> correctes,", "incorrect": "erreurs"}, "saved": {"position": {"warning": "Position sauvegardée current_question", "clamped": "est hors limites. Limitée à"}}, "resumed": {"title": "Test repris", "message": "Vous continuez à partir de la question"}, "submitted": {"score": "Vous avez obtenu", "out": "points sur", "questions": "questions", "tcf": "Score TCF (sur 699) :", "correct": "Réponses correctes :", "incorrect": "Questions incorrectes", "question": "Q", "yourAnswer": "Votre réponse :", "correctAnswer": "Bonne réponse :", "error": "Erreur lors de la soumission du test."}, "noQuestionsFound": "Aucune question trouvée pour ce test.", "questionNotFound": "Question", "features": {"hint": {"title": "Découvrez nos outils !", "analysis": "Analyse", "notebook": "Notes", "description": "Cliquez sur les boutons sur les côtés pour accéder aux outils"}}, "gradingResults": {"title": "Résultats du test", "correct": "Correct", "outOf": "sur", "questions": "questions", "tcfScore": "Score TCF", "tcfLevel": "Niveau TCF", "correctAnswers": "Bonnes répo<PERSON>s", "incorrectAnswers": "Mauvaises réponses", "reviewIncorrect": "Revoir les questions incorrectes", "question": "Question", "yourAnswer": "Votre réponse", "correctAnswer": "Bonne réponse", "saveToCollection": "Enregistrer dans le carnet de collection", "saveToCollectionDescription": "Enregis<PERSON>z toutes les {{count}} questions incorrectes dans votre collection pour les revoir et vous entraîner plus tard.", "addToCollectionButton_one": "Ajouter 1 question à la collection", "addToCollectionButton_other": "Ajouter {{count}} questions à la collection", "addingToCollection": "Ajout à la collection...", "addedToCollection": "Ajouté à la collection !", "questionsAdded": "questions ajoutées à votre collection !", "questionsAlreadySaved": "questions étaient déjà enregistrées.", "allAlreadyInCollection": "Toutes les questions étaient déjà dans votre collection.", "someIssues": "Des problèmes sont survenus :", "viewCollection": "Voir la collection", "closeButton": "<PERSON><PERSON><PERSON>", "collectionNotAvailable": "La collection n'est pas disponible pour ce type de test pour le moment.", "errors": {"noValidQuestions": "Aucune question avec un identifiant de base de données valide trouvée pour la collection. Cela peut être dû au fait que ce type de test n'est pas encore pris en charge.", "failedToAdd": "Échec de l'ajout des questions à la collection. ", "tryAgain": "Veuillez réessayer ou contacter le support si le problème persiste."}, "removeCorrectFromCollection": "<PERSON><PERSON><PERSON> toutes les réponses correctes du carnet"}}, "payments": {"success": {"title": "Paiement ré<PERSON> !", "description": "Votre abonnement a été activé avec succès. Vous avez maintenant accès à tous les tests premium.", "membershipUpdated": "Votre abonnement a été mis à jour automatiquement", "sessionId": "ID de session : {{sessionId}}", "backToHome": "Retour à l'accueil", "viewProfile": "Voir mon profil"}, "cancel": {"title": "Paiement annulé", "description": "Votre paiement a été annulé. Aucune charge n'a été effectuée sur votre compte.", "tryAgainInfo": "Vous pouvez réessayer le paiement à tout moment ou explorer nos tests gratuits.", "backToHome": "Retour à l'accueil", "tryAgain": "<PERSON><PERSON><PERSON><PERSON>"}}, "redeemPromo": {"title": "Utiliser un code promo", "description": "Entrez votre code promo pour obtenir un accès premium gratuit", "label": "Code promo", "placeholder": "ex: WELCOME3", "submit": "Utiliser le code", "submitting": "Application...", "success": "Code promo appliqué !", "redirecting": "Redirection vers votre profil dans quelques secondes...", "error": "<PERSON><PERSON><PERSON>", "enterCode": "Veuillez entrer un code promo", "loginRequired": "Connexion requise", "mustBeLoggedIn": "Vous devez être connecté pour utiliser un code promo.", "login": "Se connecter", "howItWorks": "Comment ça marche ?", "rule1": "Chaque code promo vous donne des jours d'accès premium gratuits", "rule2": "Les jours sont ajoutés à votre date d'expiration actuelle", "rule3": "Chaque code ne peut être utilisé qu'une seule fois par utilisateur", "rule4": "Maximum 3 codes promo par compte", "backToMembership": "Retour aux abonnements", "myProfile": "Mon profil", "usedCodes": "Codes déjà utilisés", "yourStatus": "Votre statut", "codesUsed": "Codes utilisés"}, "resetPassword": {"title": "Réinitialiser le mot de passe", "subtitle": "Entrez votre nouveau mot de passe", "newPassword": "Nouveau mot de passe", "confirmPassword": "Confirmer le mot de passe", "newPasswordPlaceholder": "Votre nouveau mot de passe", "confirmPasswordPlaceholder": "Confirmez votre nouveau mot de passe", "submit": "Réinitialiser le mot de passe", "success": "Su<PERSON>ès", "successMessage": "Votre mot de passe a été réinitialisé avec succès.", "invalidLink": "Lien de réinitialisation invalide. Veuillez demander un nouveau lien.", "missingToken": "Token de réinitialisation manquant", "error": "Erreur lors de la réinitialisation du mot de passe", "validation": {"minLength": "Le mot de passe doit contenir au moins 6 caractères", "noMatch": "Les mots de passe ne correspondent pas"}}, "authCallback": {"error": "Erreur lors de la récupération de la session", "noToken": "Aucun token d'accès trouvé", "authFailed": "Authentification échouée", "backToLogin": "Retour à la connexion"}, "emailVerification": {"title": "Vérifiez votre email", "subtitle": "Entrez le code de vérification envoyé à votre adresse email", "missingEmail": "Email manquant. Veuillez vous inscrire à nouveau.", "backToRegister": "Retour à l'inscription", "codeLabel": "Code de vérification", "codeSent": "Un code de vérification à 6 chiffres a été envoyé à :", "codeValid": "Code valide encore :", "codeExpired": "Le code a expiré. Veuillez en demander un nouveau.", "verifyButton": "Vérifier mon email", "noCodeReceived": "Vous n'avez pas reçu le code ?", "resendCode": "Renvoyer le code", "canResendIn": "Vous pourrez demander un nouveau code dans {{time}}", "tips": {"title": "Conseils :", "checkSpam": "Vérifiez votre dossier spam/courrier indésirable", "validFor": "Le code est valide pendant 15 minutes", "caseSensitive": "Les codes sont sensibles à la casse"}, "wrongEmail": "Mauvaise adresse email ?", "registerAgain": "S'inscrire à nouveau", "success": {"title": "Email vérifié !", "message": "Votre compte a été activé avec succès."}, "error": {"title": "<PERSON><PERSON><PERSON>", "invalidCode": "Code de vérification invalide", "resendFailed": "Erreur lors du renvoi du code"}, "resend": {"title": "Code renvoyé", "message": "Un nouveau code de vérification a été envoyé."}}, "speakingTask2": {"title": "Parties de Simulation - Expression Orale", "subtitle": "Tâche 2", "backToSpeaking": "Retour à l'Expression Orale", "parties": "Parties", "scenarios": "Scénarios", "instruction": "Pour chaque partie, lisez attentivement la situation présentée, puis utilisez les questions d'exemple pour guider votre conversation. <PERSON><PERSON> questions vous aideront à développer un dialogue naturel et cohérent.", "freePreview": "Vous voyez actuellement l'aperçu gratuit avec les parties de base.", "premiumRequired": "Les questions d'exemple détaillées nécessitent un abonnement premium pour un accès complet.", "getFullAccess": "Obtenez un accès complet aux questions détaillées pour guider votre pratique de conversation.", "dataNotFound": "Données non trouvées pour ce mois", "loadingError": "Erreur lors du chargement des données", "dataUnavailable": "Données non disponibles", "exampleQuestions": "Questions d'exemple :"}, "readingListeningTests": {"sections": {"listening": {"title": "Compréhension Orale", "description": "Testez votre capacité à comprendre le français parlé."}, "reading": {"title": "Compréhension Écrite", "description": "Testez votre capacité à comprendre le français écrit."}, "writing": {"title": "Expression Écrite", "description": "Testez votre capacité à vous exprimer en français écrit."}, "speaking": {"title": "Expression Orale", "description": "Testez votre capacité à vous exprimer en français oral."}}, "levels": {"a1": "Niveau débutant", "a2": "Niveau élémentaire", "b1": "Niveau intermédiaire", "b2": "Niveau intermédiaire avancé", "c1": "<PERSON><PERSON><PERSON> a<PERSON>cé", "c2": "<PERSON><PERSON><PERSON>", "intermediate": "Intermédiaire", "intermediateAdvanced": "Intermédiaire +", "advanced": "<PERSON><PERSON><PERSON>"}, "breadcrumb": {"home": "Accueil"}, "sectionTitles": {"freeTests": "Tests Gratuits ({{count}})", "allTests": "<PERSON><PERSON> les <PERSON> ({{count}})", "practiceByLevel": "Pratique par Niveau"}, "practiceByLevel": {"infoMessage": "La pratique par niveau contient des questions dédupliquées et classifiées de tous les tests. Chaque niveau couvre des plages de questions spécifiques (A1 à C2), vous permettant de vous concentrer sur votre niveau de difficulté cible."}, "mockExams": {"title": "Examens Simulés", "subtitle": "Examens Simulés TCF Canada", "description": "Préparez-vous avec des examens complets de 39 questions aléatoires", "createExam": "C<PERSON>er un Examen", "aboutTitle": "À propos des examens simulés", "aboutDescription": "Les examens simulés contiennent 39 questions aléatoires provenant des tests de groupe dédupliqués. Chaque examen suit la structure officielle du TCF Canada avec des questions de différents niveaux (A1 à C2). Vous pouvez avoir maximum {{maxAllowed}} examens simulés en même temps.", "premiumRequired": "Les examens simulés sont réservés aux membres Premium.", "premiumDescription": "Ils offrent une expérience complète avec 39 questions aléatoires", "noExamsTitle": "Aucun examen simulé créé", "noExamsDescription": "Cliquez sur \"Créer un Examen\" pour commencer", "examCount": "{{count}} / {{max}} examens simulés", "createAccount": "C<PERSON>ez un compte gratuit pour accéder aux tests et suivre votre progression.", "createdOn": "<PERSON><PERSON><PERSON> {{date}}", "completed": "<PERSON><PERSON><PERSON><PERSON>", "randomQuestions": "39 questions aléatoires", "recreate": "Re<PERSON>réer avec de nouvelles questions", "recreateTooltip": "Re<PERSON>réer avec de nouvelles questions", "deleteTooltip": "<PERSON><PERSON><PERSON><PERSON>"}, "errors": {"loadTests": "Impossible de charger les tests. Veuillez réessayer plus tard.", "loadMockExams": "Impossible de charger les examens simulés", "limitReached": "Limite atteinte", "maxExamsReached": "Vous ne pouvez avoir que {{maxAllowed}} examens simulés maximum. Supprimez-en un pour en créer un nouveau.", "createMockExam": "Erreur lors de la création de l'examen simulé", "deleteMockExam": "Impossible de supprimer l'examen simulé", "recreateMockExam": "Erreur lors de la recréation de l'examen simulé", "examExists": "Examen déjà existant", "examExistsMessage": "L'examen {{examName}} existe déjà. Vous pouvez le supprimer depuis la liste ci-dessous et essayer à nouveau, ou nous contacter si le problème persiste.", "loadingTitle": "Erreur de chargement"}, "success": {"created": "Su<PERSON>ès", "createdMessage": "{{examName}} créé avec {{questionCount}} questions", "deleted": "Supprimé", "deletedMessage": "Examen simulé supprimé avec succès", "recreated": "<PERSON><PERSON><PERSON><PERSON> avec succès", "recreatedMessage": "{{examName}} recréé avec {{questionCount}} nouvelles questions", "historyDeleted": "Historique supprimé", "historyDeletedMessage": "L'historique du test a été supprimé avec succès. Vous pouvez maintenant reprendre le test.", "levelHistoryDeleted": "L'historique du test de niveau {{level}} a été supprimé avec succès. Vous pouvez maintenant reprendre le test."}, "access": {"limitedTitle": "Accès <PERSON>", "freeOnly": "<PERSON><PERSON> avez accès aux tests gratuits uniquement. ", "expiredSubscription": "Votre abonnement a expiré. Renouvelez pour accéder à tous les tests. ", "loadingTitle": "Chargement des données de progression...", "loadingMessage": "Les tests de base sont affichés. Chargement des données de progression et historique en cours...", "becomePremium": "Devenir Membre Premium", "renewSubscription": "Renouveler l'Abonnement"}, "testCard": {"completedGraded": "Term<PERSON><PERSON> et noté", "completed": "<PERSON><PERSON><PERSON><PERSON>", "progress": "{{percent}}% complété", "readyToStart": "<PERSON><PERSON><PERSON><PERSON> à commencer", "subscriptionRequired": "Abonnement requis", "questions": "{{count}} questions", "results": "Résultats", "continue": "<PERSON><PERSON><PERSON>", "start": "Commencer", "resetTitle": "Réinitialiser le test", "resetMessage": "Êtes-vous sûr de vouloir supprimer tout l'historique de ce test ?", "resetWarning": "Cette action est irréversible et vous devrez recommencer le test depuis le début.", "resetResultsWarning": "Attention : Vous perdrez vos résultats actuels : {{score}}/{{maxScore}} ({{correct}} bonnes, {{wrong}} mauvaises réponses)", "resetLevelTitle": "Réinitialiser le test de niveau", "resetLevelMessage": "Êtes-vous sûr de vouloir supprimer tout l'historique de ce test de niveau ?", "resetLevelWarning": "Cette action est irréversible et vous devrez recommencer le test depuis le début.", "resetLevelResultsWarning": "Résultats actuels: {{score}}/{{maxScore}} ({{correct}} bonnes, {{wrong}} mauvaises réponses)"}, "callToAction": {"title": "Connectez-vous pour commencer", "subtitle": "C<PERSON>ez un compte ou connectez-vous pour accéder à tous les tests et suivre vos progrès.", "register": "<PERSON><PERSON><PERSON> un compte", "login": "Se connecter"}, "membershipUpgrade": {"title": "Débloquez les fonctionnalités Premium", "subtitle": "Passez à l'abonnement premium pour accéder à tous les tests, corrections détaillées et fonctionnalités avancées.", "upgrade": "Passer au Premium", "viewProfile": "Voir le Profil"}, "backToSections": "← Retour aux Sections", "auth": {"loginRequired": "Vous devez vous connecter d'abord pour accéder aux tests."}, "guide": {"reading": {"title": "Guide du Test de Lecture", "overview": {"title": "Aperçu du Test", "format": "Format : 39 questions à choix multiples (une réponse correcte)", "duration": "Du<PERSON><PERSON> : 60 minutes", "content": "Couvre des textes allant de courtes notices à des passages complexes plus longs avec difficulté croissante"}, "features": {"title": "Fonctionnalités", "highlight": "Fonction de Surlignage", "highlightExplanation": "Cliquez et faites glisser pour surligner les passages de texte importants pour une meilleure concentration", "notebook": "Fonction Carnet de Notes", "notebookExplanation": "Prenez des notes pendant votre pratique pour suivre les idées clés et les stratégies", "collection": "<PERSON>re de Collection", "collectionExplanation": "Sauvegardez les questions difficiles pour les réviser plus tard et suivre vos progrès", "verified": "Réponses Vérifiées (controversées signalées)", "verifiedExplanation": "Toutes les réponses vérifiées par des experts, avec les questions controversées clairement marquées", "optimized": "Pratique Optimisée (dédupliquée/classifiée)", "optimizedExplanation": "Questions dédupliquées et classifiées pour des sessions d'étude efficaces", "translation": "Traduction", "translationExplanation": "Trad<PERSON>z les passages de texte en anglais ou en chinois pour une meilleure compréhension", "mockExams": "Examens simulés avec tests générés aléatoirement", "mockExamsExplanation": "Générez des examens aléatoires de 39 questions qui simulent les conditions réelles du test"}}, "listening": {"title": "Guide du Test d'Écoute", "overview": {"title": "Aperçu du Test", "format": "Format : 39 questions à choix multiples (une réponse correcte)", "duration": "Durée : 35 minutes ; audio joué une seule fois", "content": "Inclut dialogues, annonces, entretiens—tous à rythme naturel"}, "features": {"title": "Fonctionnalités", "audio": "Qualité Audio Améliorée", "audioExplanation": "Qualité audio cristalline optimisée pour l'apprentissage des langues", "transcript": "Fonction Transcription pour révision phrase par phrase", "transcriptExplanation": "Révisez l'audio phrase par phrase avec des transcriptions synchronisées", "notebook": "Fonction Carnet de Notes", "notebookExplanation": "Prenez des notes en écoutant pour améliorer les stratégies de compréhension", "collection": "<PERSON>re de Collection", "collectionExplanation": "<PERSON>quez les questions difficiles pour des sessions de pratique ciblées", "verified": "Réponses Vérifiées (signalées si discutables)", "verifiedExplanation": "Réponses vérifiées par des experts avec les questions débattables clairement signalées", "optimized": "Pratique Optimis<PERSON>", "optimizedExplanation": "Pratique rationalisée avec contenu organisé et catégorisé", "translation": "Traduction", "translationExplanation": "Trad<PERSON>z le texte de transcription en anglais ou en chinois pour une meilleure compréhension", "mockExams": "Examens Simulés avec questions aléatoires", "mockExamsExplanation": "Créez des examens simulés aléatoires de 39 questions pour une pratique réaliste"}}, "writing": {"title": "Guide du Test d'Écriture", "overview": {"title": "Aperçu du Test", "format": "Format : 3 tâches, 60 minutes au total", "tasks": {"title": "Types de Tâches", "task1": "Message/email (60–120 mots)", "task2": "Récit court (~120–150 mots)", "task3": "Texte argumentatif (~120–180 mots)"}}, "features": {"title": "Fonctionnalités", "collection": "Collection Complète de Tâches et Corrections", "practice": "Pratique Chronométrée (Examens Simulés)", "guidance": "Guide d'Écriture"}}, "speaking": {"title": "Guide du Test d'Expression Orale", "overview": {"title": "Aperçu du Test", "duration": "Durée Totale : ~12 minutes (incluant ~2 min de préparation)", "tasks": {"title": "3 Tâches", "task1": "<PERSON><PERSON><PERSON> structuré (~2 min, sans préparation)", "task2": "Conversation interactive (2 min préparation + ~3,5 min d'échange)", "task3": "Monologue argumentatif (~4,5 min, sans préparation)"}}, "features": {"title": "Fonctionnalités", "collection": "Collection Complète de Tâches", "practice": "Sessions de Pratique Simulées", "guides": "Guides de Structure"}}}}, "speakingTask3": {"title": "Questions d'Opinion - Expression Orale", "subtitle": "Tâche 3", "backToSpeaking": "Retour à l'Expression Orale", "wordsPerResponse": "Mots/réponse", "instruction": "Lisez chaque question attentivement et réfléchissez à votre réponse personnelle.", "correctionAvailable": "Cliquez sur \"Correction\" pour voir une réponse exemplaire qui vous aidera à structurer votre argumentation.", "correctionPremium": "Les réponses exemplaires sont disponibles avec un abonnement premium.", "freePreview": "Vous voyez actuellement l'aperçu gratuit avec les questions de base.", "premiumRequired": "Les réponses exemplaires détaillées nécessitent un abonnement premium pour un accès complet.", "exemplaryAnswer": "Réponse exemplaire :", "correction": "Correction", "getFullAccess": "Obtenez un accès complet aux réponses détaillées pour améliorer votre argumentation.", "dataNotFound": "Données non trouvées pour ce sujet", "loadingError": "Erreur lors du chargement des données", "dataUnavailable": "Données non disponibles", "questions": "Questions", "words": "mots", "paragraphs": "paragraphes", "task": "<PERSON><PERSON><PERSON>", "question": "Question :", "hideCorrection": "Masquer la correction", "exemplaryAnswerAvailable": "Réponse exemplaire disponible avec Premium :", "premium": "Premium", "instructionsTitle": "Instructions"}, "writingMonthDetails": {"task": "Tâche {{number}}", "tasks": "{{count}} tâche", "tasksPlural": "{{count}} tâches", "premiumAccess": "Accès Premium activé!", "premiumMessage": "<PERSON><PERSON> avez accès à toutes les tâches et corrections de ce mois.", "freeAccess": "Accès gratuit:", "freeMessage": "Vous pouvez voir toutes les tâches de ce mois.", "combinations": "Combinaisons", "tasksLabel": "Tâches", "monthDescription": "Ce mois contient {{combinationCount}} combinaisons avec un total de {{taskCount}} tâches d'écriture.", "combinationInfo": "Chaque combinaison comprend 3 tâches différentes. Les corrections détaillées sont disponibles avec un abonnement Premium.", "loginForCorrections": "Connectez-vous et obtenez un abonnement Premium pour voir les corrections.", "upgradeForCorrections": "Obtenez un abonnement Premium pour voir les corrections.", "errors": {"missingMonthId": "ID du mois manquant", "loadingData": "Erreur lors du chargement des données du mois", "monthNotFound": "<PERSON>is non trouvé", "title": "<PERSON><PERSON><PERSON>"}, "navigation": {"backToExercises": "Retour aux Exercices"}}, "speaking": {"title": "Expression Orale", "subtitle": "Maîtrisez l'expression orale avec des scénarios pratiques et des questions d'opinion", "allTasks": "Toutes les Tâches", "accessTitle": "Accès au contenu", "freePreview": "Aperçu gratuit : Scénarios et questions de base disponibles pour tous.", "premiumContent": "Contenu premium : Questions d'exemple détaillées et réponses exemplaires nécessitent un abonnement.", "access": {"premiumActive": "Accès Premium activé!", "premiumMessage": "V<PERSON> avez accès à toutes les tâches d'expression orale avec des questions d'exemple détaillées et des réponses exemplaires.", "freeAccess": "Accès gratuit:", "freeMessage": "Vous pouvez voir toutes les tâches d'expression orale.", "loginForPremium": "Connectez-vous et obtenez un abonnement Premium pour accéder au contenu détaillé.", "upgradeForPremium": "Obtenez un abonnement Premium pour accéder au contenu détaillé."}, "sections": {"allTasks": "Toutes les Tâches ({{count}} éléments)"}, "noTasks": {"title": "Aucune tâche disponible", "message": "Aucune tâche d'expression orale n'est actuellement disponible. Veuillez réessayer plus tard."}, "backToSections": "← Retour aux Sections", "task2": {"title": "Tâche 2", "subtitle": "Scénarios de Simulation", "description": "Pratiquez des situations de communication réalistes avec des questions d'exemple pour guider votre conversation.", "monthsCount": "{{count}} mois", "parties": "{{count}} parties", "scenarios": "{{count}} s<PERSON><PERSON><PERSON>", "exampleQuestions": "Questions d'exemple", "total": "{{count}} total", "premiumRequired": "Premium requis", "fullAccess": "<PERSON><PERSON>ès complet", "previewAvailable": "Aperçu disponible", "explore": "Explorer"}, "task3": {"title": "Tâche 3", "subtitle": "Questions d'Opinion", "description": "Développez vos arguments sur des sujets variés avec des réponses exemplaires détaillées.", "subjectsCount": "{{count}} sujets", "questions": "{{count}} questions", "exemplaryAnswers": "Réponses exemplaires", "available": "Disponibles", "premiumRequired": "Premium requis", "fullAccess": "<PERSON><PERSON>ès complet", "questionsAvailable": "Questions disponibles", "explore": "Explorer"}, "statistics": {"title": "Statistiques Globales", "monthsAvailable": "<PERSON><PERSON> disponibles", "scenariosTask2": "Scénarios Tâche 2", "subjectsAvailable": "Sujets disponibles", "questionsTask3": "Questions Tâche 3"}, "errors": {"loadingData": "Erreur lors du chargement des données d'expression orale"}}, "notebook": {"placeholder": "<PERSON><PERSON> notes...", "title": "<PERSON><PERSON>", "description": "Pa<PERSON>ourez et gérez vos entrées de carnet sauvegardées", "createNew": "Créer Nouveau", "createFirst": "Créer Votre Premier Carnet", "searchPlaceholder": "Rechercher dans les carnets...", "empty": "Aucun carnet sauvegardé", "noSearchResults": "Aucun carnet trouvé correspondant à votre recherche", "universal": "Universel", "characters": "caractères", "open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "lastUpdated": "Dernière mise à jour", "confirmDelete": "Confirmer la Suppression", "deleteConfirmMessage": "Êtes-vous sûr de vouloir supprimer ce carnet ? Cette action ne peut pas être annulée.", "deleteSuccess": "Carnet supprimé", "deleteSuccessMessage": "Votre carnet a été supprimé avec succès", "deleteError": "Échec de la suppression", "deleteErrorMessage": "Échec de la suppression du carnet", "loadError": "Échec du chargement des carnets", "loadErrorMessage": "Une erreur s'est produite lors du chargement de vos carnets. Veuillez réessayer.", "loginMessage": "Veuillez vous connecter pour voir vos carnets sauvegardés.", "saveAs": "Enregistrer sous...", "saveAsDescription": "Donnez un nom à votre carnet pour l'enregistrer. Votre carnet global actuel sera vidé.", "notebookName": "Nom du Carnet", "namePlaceholder": "Entrez le nom du carnet...", "save": "Enregistrer", "nameRequired": "Veuillez entrer un nom de carnet", "saveSuccess": "Carnet Enregistré", "saveSuccessMessage": "Le carnet a été enregistré avec succès", "saveError": "Échec de l'Enregistrement", "saveErrorMessage": "Échec de l'enregistrement du carnet", "nameExists": "Un carnet avec ce nom existe déjà", "noContent": "Aucun contenu à enregistrer", "emptyContent": "Impossible d'enregistrer un carnet vide", "error": "<PERSON><PERSON><PERSON>", "edit": "Modifier", "editNotebook": "Modifier le Carnet", "editDescription": "Modifiez le contenu de votre carnet ci-dessous. Les modifications seront sauvegardées automatiquement.", "saveChanges": "Sauvegarder les Modifications", "updateSuccess": "Carnet mis à jour", "updateSuccessMessage": "Votre carnet a été mis à jour avec succès", "updateError": "Échec de la mise à jour", "updateErrorMessage": "Échec de la mise à jour du carnet", "deleteModal": {"title": "Supprimer toutes les notes", "message": "Êtes-vous sûr de vouloir supprimer toutes vos notes ? Cette action est irréversible.", "cancel": "Annuler", "confirm": "Supp<PERSON>er tout"}}, "modificationNotification": {"answerUpdates": "Mises à jour des réponses ({{count}})", "listeningModifications": "Modifications des réponses d'écoute", "readingModifications": "Modifications des réponses de lecture", "summaryMessage": "Certaines réponses de test ont été révisées et mises à jour pour plus de précision. Veuillez consulter le journal des modifications pour plus de détails.", "recentChanges": "Modifications récentes :"}, "analysis": {"title": "Analyse", "notAvailable": "Aucune analyse disponible pour cette question", "noContent": "Aucun contenu d'analyse disponible dans cette langue"}, "downloads": {"title": "Matériel d'Étude GRATUIT", "subtitle": "Téléchargez des matériaux de préparation TCF Canada complets sans frais", "description": "Accédez à notre collection complète de guides d'étude, tests pratiques et documents de référence. Tous les matériaux sont disponibles via Google Drive et Baidu Wangpan pour votre commodité.", "downloadButton": "Télécharger", "loginRequired": {"title": "Connexion Requise", "message": "Vous devez être connecté pour accéder à la page de téléchargements.", "button": "Aller à la Connexion"}, "categories": {"listening": {"title": "Matériel d'Écoute", "description": "Fichiers audio, transcriptions et tests pratiques pour la préparation à la compréhension orale."}, "reading": {"title": "<PERSON><PERSON><PERSON>", "description": "Passages de texte, exercices de compréhension et guides stratégiques pour le développement des compétences de lecture."}, "writing": {"title": "Matériel d'Écriture", "description": "Exemples de tâches, sujets de dissertation et exercices de pratique d'écriture structurée pour TCF Canada.\n\nCode d'extraction Bai<PERSON>: chez"}, "speaking": {"title": "Matériel d'Expression Orale", "description": "Sujets de conversation, scénarios de tâches orales et guides de pratique d'expression orale.\n\nCode d'extraction <PERSON><PERSON>: chez"}}, "info": {"title": "Informations de Téléchargement", "description": "Tous les matériaux sont régulièrement mis à jour et entièrement gratuits. Choisissez votre plateforme de téléchargement préférée ci-dessous. Google Drive offre un aperçu facile et le partage, tandis que Baidu Wangpan fournit des téléchargements plus rapides pour les utilisateurs en Chine.", "lastUpdated": "Dernière mise à jour"}}, "translation": {"title": "Traduction", "originalText": "Texte original", "selectLanguage": "Sélectionnez une langue pour traduire", "copy": "Copier la traduction", "copied": "Copié !", "copiedToClipboard": "Traduction copiée dans le presse-papiers", "error": "Erreur de traduction", "failed": "Échec de la traduction", "networkError": "<PERSON><PERSON><PERSON> r<PERSON><PERSON> survenue", "copyFailed": "Échec de la copie dans le presse-papiers", "translate": "<PERSON><PERSON><PERSON><PERSON>"}, "writing": {"title": "Expression Écrite", "subtitle": "Collection complète d'exercices d'écriture organisés par mois. Les tâches sont gratuites, les corrections nécessitent un abonnement Premium.", "navigation": {"title": "Tests d'Écriture", "subtitle": "Exercices mensuels", "expand": "Développer la navigation", "collapse": "Réduire la navigation", "allTests": "<PERSON><PERSON>", "bookmarked": "<PERSON><PERSON><PERSON>", "allMonths": "<PERSON><PERSON> les Mois", "loading": "Chargement...", "openMenu": "Ouv<PERSON>r la navigation d'écriture", "current": "Actuel", "previous": "Précédent", "next": "Suivant", "previousMonth": "<PERSON><PERSON>", "nextMonth": "<PERSON><PERSON> suivant", "navigateToMonth": "Naviguer vers {{month}}", "tasksCount": "{{count}} tâche", "tasksCount_plural": "{{count}} tâches"}, "monthCard": {"combinations": "{{count}} combinaisons", "tasks": "{{count}} tâches", "content": "Contenu", "total": "{{count}} total", "readyToUse": "<PERSON>rêt à utiliser", "explore": "Explorer"}, "access": {"premiumActive": "Accès Premium activé !", "premiumMessage": "V<PERSON> avez accès à toutes les tâches d'écriture et leurs corrections détaillées.", "freeAccess": "Accès gratuit :", "freeMessage": "Vous pouvez voir toutes les tâches d'écriture.", "loginForCorrections": "Connectez-vous et obtenez un abonnement Premium pour accéder aux corrections détaillées.", "upgradeForCorrections": "Obtenez un abonnement Premium pour accéder aux corrections détaillées."}, "statistics": {"months": "<PERSON><PERSON>", "combinations": "Combinaisons", "tasks": "Tâches"}, "sections": {"allTasks": "Toutes les Tâches ({{count}} mois)"}, "noExercises": {"title": "Aucun exercice disponible", "message": "Aucun exercice d'écriture n'est actuellement disponible. Veuillez réessayer plus tard."}, "backToSections": "← Retour aux Sections", "taskDetails": {"task": "Tâche {{number}}", "viewDetails": "Voir détails", "hide": "Masquer", "correction": "Correction", "premiumRequired": "Premium requis", "correctionLabel": "Correction:", "instructionLabel": "Consigne:", "correctionAvailable": "Correction disponible avec l'abonnement Premium", "combinationTask": "Combinaison {{combination}} - Tâche {{task}}", "writeResponse": "R<PERSON><PERSON>ger une réponse"}, "editor": {"taskPrompt": "<PERSON><PERSON><PERSON>", "words": "mots", "placeholder": "Commencez à écrire votre réponse ici...", "loadingContent": "Chargement de votre travail précédent...", "save": "Sauvegarder le brouillon", "submit": "Soumettre la version finale", "saving": "Sauvegarde en cours", "lastSaved": "<PERSON><PERSON><PERSON> sauve<PERSON>", "unsaved": "Modifications non sauvegardées", "viewHistory": "Voir l'historique des soumissions", "submissionHistory": "Historique des soumissions", "final": "Final", "draft": "Brouillon", "submitted": "<PERSON><PERSON>s avec succès", "noSubmissions": "Aucune soumission pour le moment", "finalSubmissionExists": "Soumission finale terminée", "finalSubmissionMessage": "V<PERSON> avez soumis votre réponse finale le {{date}} avec {{words}} mots.", "saved": "Brouillon sauvegardé", "savedMessage": "Votre brouillon a été sauvegardé avec succès.", "submittedMessage": "Votre rédaction a été soumise avec succès.", "error": "<PERSON><PERSON><PERSON>", "emptyContent": "Veuillez écrire quelque chose avant de sauvegarder ou soumettre.", "saveError": "Échec de la sauvegarde de votre brouillon. Veuillez réessayer.", "submitError": "Échec de la soumission de votre rédaction. Veuillez réessayer.", "minWordsError": "Vous avez besoin d'au moins {{min}} mots. Actuellement : {{current}} mots.", "maxWordsError": "Vous avez dépassé le maximum de {{max}} mots. Actuellement : {{current}} mots.", "needMoreWords": "Il faut {{needed}} mots de plus pour atteindre le minimum", "tooManyWords": "{{excess}} mots au-dessus de la limite maximale"}}, "error": {"audio": {"title": "Erreur Audio", "loading": "Échec du chargement du fichier audio", "network": "<PERSON><PERSON><PERSON> réseau lors du chargement de l'audio", "notFound": "Fichier audio introuvable"}, "image": {"title": "Erreur Image", "loading": "Échec du chargement du fichier image", "network": "<PERSON><PERSON><PERSON> réseau lors du chargement de l'image", "notFound": "Fichier image introuvable"}, "premium": {"required": "Abonnement Premium Requis", "audio": "Ce contenu audio nécessite un abonnement premium pour y accéder.", "image": "Ce contenu image nécessite un abonnement premium pour y accéder.", "upgrade": "Passer au Premium"}, "promo": {"alreadyUsed": "Vous avez déjà utilisé ce code promo. Si vous pensez qu'il s'agit d'une erreur, veuil<PERSON><PERSON> contacter le support."}}, "classifiedWriting": {"infoMessage": "La pratique par tâche contient des tâches d'écriture dédupliquées et classifiées de tous les tests. Chaque tâche couvre des types de tâches et des thèmes spécifiques, vous permettant de vous concentrer sur vos compétences d'écriture cibles.", "title": "Tâches Classifiées", "subtitle": "Explorez les tâches d'écriture organisées par thèmes et sujets avec déduplication intelligente", "loading": {"cards": "Chargement des tâches d'écriture classifiées...", "overview": "Chargement de l'aperçu...", "topics": "Chargement des sujets...", "tasks": "Chargement des tâches..."}, "errors": {"loadCards": "Échec du chargement des cartes d'écriture classifiées", "loadOverview": "Erreur lors du chargement de l'aperçu de la tâche", "loadTopics": "Erreur lors du chargement des détails du sujet", "loadTasks": "Erreur lors du chargement des tâches", "search": "Erreur lors de la recherche de tâches"}, "noData": {"title": "<PERSON><PERSON><PERSON>", "message": "Aucune tâche d'écriture classifiée n'est actuellement disponible."}, "card": {"totalTasks": "Tâches totales", "uniqueTasks": "Tâches uniques", "topics": "Sujets", "deduplication": "Déduplication", "topTopics": "Principaux sujets", "moreTopics": "autres sujets", "explore": "Explorer", "premiumRequired": "Premium Requis", "loginRequired": "Connexion Requise", "noTasks": "Aucune tâche disponible"}, "taches": {"1": {"description": "Rédaction de messages personnels (60-120 mots)"}, "2": {"description": "Rédaction d'articles informatifs (120-150 mots)"}, "3": {"description": "Rédaction d'essais argumentatifs (120-180 mots)"}}, "overview": {"title": "Vue d'ensemble", "description": "Sélectionnez un thème pour voir les sous-thèmes disponibles", "tasks": "tâches"}, "auth": {"loginRequired": "Connexion requise pour accéder aux tâches classifiées"}, "premium": {"title": "Fonctionnalité Premium", "description": "Accédez à plus de 1000 tâches d'écriture organisées par thèmes avec déduplication intelligente. Parfait pour une pratique ciblée et efficace."}, "task": {"content": "Instructions", "wordLimit": "<PERSON><PERSON>", "startWriting": "Commencer à écrire", "membershipRequired": "Adhésion requise"}, "topics": {"recommendation": "Recommandation", "description_places": "Description de lieux", "description_person": "Description de personne", "description_object": "Description d'objet", "narration": "Narration", "opinion": "Opinion", "explanation": "Explication", "argumentation": "Argumentation", "comparison": "Comparaison", "analysis": "Analyse"}, "navigation": {"expand": "Développer", "collapse": "<PERSON><PERSON><PERSON><PERSON>"}, "subtopic": {"description": "Rédigez une réponse modèle qui couvre ce type de tâche"}, "template": {"label": "Template", "writeResponse": "Votre réponse modèle", "description": "Écrivez une réponse qui couvre ce type de tâche. Votre réponse sera applicable à tous les exemples similaires.", "viewExamples": "Voir les exemples de tâches", "startWriting": "Écrire pour ce template", "save": "Sauvegarder le template", "saveSuccess": "Template sauvegardé avec succès!", "saveError": "<PERSON><PERSON>ur lors de la sauvegarde du template", "placeholder": "Écrivez votre réponse template ici. Cette réponse devrait couvrir le pattern général de ce type de tâche...", "words": "mots", "autoSaving": "Sauvegarde automatique...", "autoSaved": "Sauvegardé automatiquement", "autoSaveError": "<PERSON><PERSON><PERSON> <PERSON> sauve<PERSON>"}, "examples": {"title": "Exemples de tâches", "description": "Utilisez ces exemples comme référence pour comprendre le pattern de ce type de tâche.", "example": "Exemple", "count": "exemples", "taskId": "Identifiant de la tâche", "taskIds": "Identifiants des tâches", "moreExamples": "... et {{count}} autres exemples similaires", "noTasks": "Aucun exemple disponible"}}}