import { useState } from 'react';
import { Container, Paper, Title, TextInput, Button, Stack, Group, Text, Alert, List, Badge, Divider } from '@mantine/core';
import { IconTicket, IconInfoCircle, IconCheck, IconArrowLeft, IconUser, IconAlertCircle } from '@tabler/icons-react';
import { useMutation } from '@tanstack/react-query';
import { notifications } from '@mantine/notifications';
import { Link, useNavigate } from 'react-router-dom';
import { paymentsApi } from '../services/api';
import { useAuthStore } from '../store/useAuthStore';
import { formatDateSafely, formatDateWithUserTimezone } from '../utils/dateUtils';
import { useTranslation } from 'react-i18next';

export function RedeemPromo() {
  const [promoCode, setPromoCode] = useState('');
  const [lastError, setLastError] = useState<string | null>(null);
  const [successData, setSuccessData] = useState<any>(null);
  const navigate = useNavigate();
  const { isAuthenticated, updateUser, user } = useAuthStore();
  const { t } = useTranslation();

  const redeemMutation = useMutation({
    mutationFn: paymentsApi.redeemPromoCode,
    onSuccess: (data) => {
      setSuccessData(data);
      setLastError(null);

      // Handle internationalized success message
      let successMessage = data.message;
      if (data.message_key && data.message_data) {
        try {
          // Get the translated action word
          const actionKey = data.action === 'prolongé' ? 'extended' : 'activated';
          const translatedAction = t(`redeemPromo.actions.${actionKey}`);

          // Create the translated message
          successMessage = t(data.message_key, {
            code: data.message_data.code,
            action: translatedAction,
            days: data.message_data.days,
            plural: data.message_data.days > 1 ? 's' : ''
          });
        } catch (e) {
          // Fallback to original message if translation fails
          console.warn('Translation failed for success message:', data.message_key);
        }
      }

      notifications.show({
        title: t('redeemPromo.success'),
        message: successMessage,
        color: 'green',
        icon: <IconCheck size={16} />,
        autoClose: 7000,
      });
      
      // Update user membership status and used promo codes
      const currentUsedCodes = user?.used_promo_codes || [];
      const updatedUsedCodes = [...currentUsedCodes, promoCode.trim().toUpperCase()];
      
      updateUser({ 
        membership_expires_at: data.membership_expires_at,
        membership_type: 'premium',
        used_promo_codes: updatedUsedCodes
      });
      
      // Clear the form
      setPromoCode('');
      
      // Redirect to profile after a short delay
      setTimeout(() => {
        navigate('/profile');
      }, 3000);
    },
    onError: (error: any) => {
      console.error('Promo redemption error:', error);

      // Check if the error has an internationalization key
      const errorData = error.response?.data;
      let errorMessage = errorData?.error || t('redeemPromo.error');

      // Use i18n key if available
      if (errorData?.error_key) {
        try {
          const translatedMessage = t(errorData.error_key);
          // Only use translated message if it's different from the key (meaning translation exists)
          if (translatedMessage !== errorData.error_key) {
            errorMessage = translatedMessage;
          }
        } catch (e) {
          // Fallback to original error message if translation fails
          console.warn('Translation failed for error key:', errorData.error_key);
        }
      }

      setLastError(errorMessage);
      setSuccessData(null);

      notifications.show({
        title: t('redeemPromo.error'),
        message: errorMessage,
        color: 'red',
        icon: <IconAlertCircle size={16} />,
        autoClose: 7000,
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!promoCode.trim()) {
      const message = t('redeemPromo.enterCode');
      setLastError(message);
      notifications.show({
        title: t('redeemPromo.error'),
        message,
        color: 'red',
      });
      return;
    }
    
    setLastError(null);
    setSuccessData(null);
    redeemMutation.mutate(promoCode.trim());
  };

  if (!isAuthenticated) {
    return (
      <Container size="sm" py="xl">
        <Alert icon={<IconInfoCircle size={16} />} title={t('redeemPromo.loginRequired')} color="blue">
          {t('redeemPromo.mustBeLoggedIn')}{' '}
          <Link to="/login" style={{ color: 'inherit' }}>
            {t('redeemPromo.login')}
          </Link>
        </Alert>
      </Container>
    );
  }

  return (
    <Container size="md" py="xl">
      <Stack gap="xl">
        <Paper shadow="sm" p="xl" radius="md" withBorder>
          <Stack gap="lg">
            <div style={{ textAlign: 'center' }}>
              <Group justify="center" mb="sm">
                <IconTicket size={32} color="var(--mantine-color-blue-6)" />
                <Title order={2}>{t('redeemPromo.title')}</Title>
              </Group>
              <Text c="dimmed">
                {t('redeemPromo.description')}
              </Text>
            </div>

            {/* Success Message */}
            {successData && (
              <Alert icon={<IconCheck size={16} />} title={t('redeemPromo.success')} color="green">
                <Text>
                  {(() => {
                    // Handle internationalized success message for display
                    if (successData.message_key && successData.message_data) {
                      try {
                        const actionKey = successData.action === 'prolongé' ? 'extended' : 'activated';
                        const translatedAction = t(`redeemPromo.actions.${actionKey}`);

                        return t(successData.message_key, {
                          code: successData.message_data.code,
                          action: translatedAction,
                          days: successData.message_data.days,
                          plural: successData.message_data.days > 1 ? 's' : ''
                        });
                      } catch (e) {
                        return successData.message;
                      }
                    }
                    return successData.message;
                  })()}
                </Text>
                <Text size="sm" mt="xs">
                  {t('redeemPromo.redirecting')}
                </Text>
              </Alert>
            )}

            {/* Error Message */}
            {lastError && (
              <Alert icon={<IconAlertCircle size={16} />} title={t('redeemPromo.error')} color="red">
                {lastError}
              </Alert>
            )}

            <form onSubmit={handleSubmit}>
              <Stack gap="md">
                <TextInput
                  label={t('redeemPromo.label')}
                  placeholder={t('redeemPromo.placeholder')}
                  value={promoCode}
                  onChange={(e) => setPromoCode(e.target.value.toUpperCase())}
                  required
                  size="lg"
                  maxLength={20}
                  style={{ textTransform: 'uppercase' }}
                  error={lastError ? ' ' : null} // Show error state
                />
                
                <Button
                  type="submit"
                  size="lg"
                  loading={redeemMutation.isPending}
                  leftSection={<IconCheck size={16} />}
                  disabled={!promoCode.trim() || !!successData}
                >
                  {redeemMutation.isPending ? t('redeemPromo.submitting') : t('redeemPromo.submit')}
                </Button>
              </Stack>
            </form>

            {/* Help Section */}
            <Paper p="md" bg="gray.0" radius="md">
              <Group mb="sm">
                <IconInfoCircle size={20} color="var(--mantine-color-blue-6)" />
                <Text fw={500}>{t('redeemPromo.howItWorks')}</Text>
              </Group>
              <List size="sm" spacing="xs">
                <List.Item>{t('redeemPromo.rule1')}</List.Item>
                <List.Item>{t('redeemPromo.rule2')}</List.Item>
                <List.Item>{t('redeemPromo.rule3')}</List.Item>
                <List.Item>{t('redeemPromo.rule4')}</List.Item>
              </List>
            </Paper>

            {/* Navigation */}
            <Group justify="center" gap="md">
              <Link to="/membership" style={{ textDecoration: 'none' }}>
                <Button variant="outline" leftSection={<IconArrowLeft size={16} />}>
                  {t('redeemPromo.backToMembership')}
                </Button>
              </Link>
              <Link to="/profile" style={{ textDecoration: 'none' }}>
                <Button variant="outline" leftSection={<IconUser size={16} />}>
                  {t('redeemPromo.myProfile')}
                </Button>
              </Link>
            </Group>
          </Stack>
        </Paper>

        {/* User Status */}
        {user && (
          <Paper shadow="sm" p="lg" radius="md" withBorder>
            <Title order={4} mb="md">{t('redeemPromo.yourStatus')}</Title>
            <Stack gap="sm">
              <Group>
                <Text fw={500}>{t('redeemPromo.codesUsed')}:</Text>
                <Text>{user.used_promo_codes?.length || 0}/3</Text>
              </Group>
              
              {user.used_promo_codes && user.used_promo_codes.length > 0 && (
                <div>
                  <Text size="sm" c="dimmed" mb="xs">{t('redeemPromo.usedCodes')}:</Text>
                  <Group gap="xs">
                    {user.used_promo_codes.map((code, index) => (
                      <Badge key={`${code}-${index}`} color="gray" variant="light">
                        {code}
                      </Badge>
                    ))}
                  </Group>
                </div>
              )}
              
              {user.membership_type === 'premium' && user.membership_expires_at && (
                <Group>
                  <Text fw={500}>{t('profile.expiresOn')}:</Text>
                  <Text c="blue">
                    {formatDateWithUserTimezone(user.membership_expires_at)}
                  </Text>
                </Group>
              )}
            </Stack>
          </Paper>
        )}
      </Stack>
    </Container>
  );
} 