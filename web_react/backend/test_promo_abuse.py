#!/usr/bin/env python3
"""
Test script for promo code abuse detection functionality.

This script tests the anti-abuse system for the 'tcfca' promo code to ensure
it correctly prevents multiple accounts from the same IP from using the promo.

Usage:
    python test_promo_abuse.py
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.utils.promo_abuse_detector import PromoAbuseDetector


def test_promo_abuse_detection():
    """Test the promo abuse detection functionality"""
    
    print("🧪 Testing Promo Code Abuse Detection System")
    print("=" * 50)
    
    # Create app context
    app = create_app('development')
    
    with app.app_context():
        # Test cases
        test_cases = [
            {
                'name': 'Test 1: Clean IP (no previous usage)',
                'ip': '*************',
                'promo': 'TCFCA',
                'user_id': 'test-user-1',
                'expected_abuse': False
            },
            {
                'name': 'Test 2: IP with previous TCFCA usage',
                'ip': '*************',
                'promo': 'TCFCA', 
                'user_id': 'test-user-2',
                'expected_abuse': True  # Assuming this IP has been used before
            },
            {
                'name': 'Test 3: Different promo code (should not be restricted)',
                'ip': '*************',
                'promo': 'WELCOME3',
                'user_id': 'test-user-3',
                'expected_abuse': False
            },
            {
                'name': 'Test 4: Unknown IP',
                'ip': 'unknown',
                'promo': 'TCFCA',
                'user_id': 'test-user-4',
                'expected_abuse': False
            }
        ]
        
        results = []
        
        for test_case in test_cases:
            print(f"\n🔍 {test_case['name']}")
            print(f"   IP: {test_case['ip']}")
            print(f"   Promo: {test_case['promo']}")
            
            try:
                is_abuse, reason = PromoAbuseDetector.check_ip_abuse_for_promo(
                    test_case['ip'], 
                    test_case['promo'], 
                    test_case['user_id']
                )
                
                print(f"   Result: {'🚨 ABUSE DETECTED' if is_abuse else '✅ ALLOWED'}")
                print(f"   Reason: {reason}")
                
                # Check if result matches expectation
                if is_abuse == test_case['expected_abuse']:
                    print(f"   Status: ✅ PASS")
                    results.append(True)
                else:
                    print(f"   Status: ❌ FAIL (Expected abuse={test_case['expected_abuse']}, got {is_abuse})")
                    results.append(False)
                    
            except Exception as e:
                print(f"   Status: ❌ ERROR - {str(e)}")
                results.append(False)
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 Test Summary")
        print(f"Total tests: {len(results)}")
        print(f"Passed: {sum(results)}")
        print(f"Failed: {len(results) - sum(results)}")
        
        if all(results):
            print("🎉 All tests passed!")
        else:
            print("⚠️  Some tests failed. Check the implementation.")
        
        return all(results)


def test_promo_stats():
    """Test the promo statistics functionality"""

    print("\n🔍 Testing Promo Statistics Functionality")
    print("=" * 50)

    app = create_app('development')

    with app.app_context():
        # Test with different promo codes
        test_promos = ['TCFCA', 'WELCOME3', 'NONEXISTENT']

        for promo in test_promos:
            print(f"\n📊 Stats for Promo: {promo}")
            try:
                stats = PromoAbuseDetector.get_promo_ip_stats(promo)

                if 'error' in stats:
                    print(f"   Error: {stats['error']}")
                else:
                    print(f"   Total IP addresses: {stats.get('total_ip_addresses', 0)}")
                    print(f"   Current uses: {stats.get('current_uses', 0)}")
                    print(f"   Max uses: {stats.get('max_uses', 0)}")
                    print(f"   Is active: {stats.get('is_active', False)}")
                    print(f"   Is IP restricted: {stats.get('is_ip_restricted', False)}")
                    print(f"   Used IPs: {stats.get('used_ips', [])}")

            except Exception as e:
                print(f"   Error: {str(e)}")


def test_record_ip_usage():
    """Test recording IP usage"""

    print("\n🔍 Testing IP Usage Recording")
    print("=" * 50)

    app = create_app('development')

    with app.app_context():
        test_ip = '192.168.1.999'  # Use a test IP that won't conflict
        test_promo = 'TCFCA'

        print(f"Testing recording IP {test_ip} for promo {test_promo}")

        try:
            # Record usage
            success = PromoAbuseDetector.record_ip_usage(test_ip, test_promo)
            print(f"Recording result: {'✅ SUCCESS' if success else '❌ FAILED'}")

            # Check if it's now detected as abuse
            is_abuse, reason = PromoAbuseDetector.check_ip_abuse_for_promo(test_ip, test_promo)
            print(f"Abuse check result: {'🚨 ABUSE DETECTED' if is_abuse else '✅ ALLOWED'}")
            print(f"Reason: {reason}")

        except Exception as e:
            print(f"Error: {str(e)}")


def main():
    """Run all tests"""
    print("🚀 Starting Promo Abuse Detection Tests")

    try:
        # Test abuse detection
        detection_passed = test_promo_abuse_detection()

        # Test promo stats
        test_promo_stats()

        # Test IP recording
        test_record_ip_usage()

        print("\n" + "=" * 60)
        if detection_passed:
            print("✅ Promo abuse detection system is working correctly!")
        else:
            print("❌ Promo abuse detection system has issues that need to be fixed.")

        print("\n💡 Next steps:")
        print("1. Run the database migration: migrations/add_ip_tracking_fields.sql")
        print("2. The system will automatically populate existing IP data from users who used promo codes")
        print("3. Test with real promo codes in your development environment")
        print("4. Monitor the admin endpoints:")
        print("   - /api/admin/promo-abuse/promo-stats?promo_code=TCFCA")
        print("   - /api/admin/promo-abuse/all-promos")
        print("5. The system will automatically block repeat usage from same IP")

    except Exception as e:
        print(f"❌ Test execution failed: {str(e)}")
        return False

    return detection_passed


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
