#!/usr/bin/env python3
"""
Test script for promo code abuse detection functionality.

This script tests the anti-abuse system for the 'tcfca' promo code to ensure
it correctly prevents multiple accounts from the same IP from using the promo.

Usage:
    python test_promo_abuse.py
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.utils.promo_abuse_detector import PromoAbuseDetector


def test_promo_abuse_detection():
    """Test the promo abuse detection functionality"""
    
    print("🧪 Testing Promo Code Abuse Detection System")
    print("=" * 50)
    
    # Create app context
    app = create_app('development')
    
    with app.app_context():
        # Test cases
        test_cases = [
            {
                'name': 'Test 1: Clean IP (no previous usage)',
                'ip': '***********00',
                'promo': 'TCFCA',
                'user_id': 'test-user-1',
                'expected_abuse': False
            },
            {
                'name': 'Test 2: IP with previous TCFCA usage',
                'ip': '***********01',
                'promo': 'TCFCA', 
                'user_id': 'test-user-2',
                'expected_abuse': True  # Assuming this IP has been used before
            },
            {
                'name': 'Test 3: Different promo code (should not be restricted)',
                'ip': '***********01',
                'promo': 'WELCOME3',
                'user_id': 'test-user-3',
                'expected_abuse': False
            },
            {
                'name': 'Test 4: Unknown IP',
                'ip': 'unknown',
                'promo': 'TCFCA',
                'user_id': 'test-user-4',
                'expected_abuse': False
            }
        ]
        
        results = []
        
        for test_case in test_cases:
            print(f"\n🔍 {test_case['name']}")
            print(f"   IP: {test_case['ip']}")
            print(f"   Promo: {test_case['promo']}")
            
            try:
                is_abuse, reason = PromoAbuseDetector.check_ip_abuse_for_promo(
                    test_case['ip'], 
                    test_case['promo'], 
                    test_case['user_id']
                )
                
                print(f"   Result: {'🚨 ABUSE DETECTED' if is_abuse else '✅ ALLOWED'}")
                print(f"   Reason: {reason}")
                
                # Check if result matches expectation
                if is_abuse == test_case['expected_abuse']:
                    print(f"   Status: ✅ PASS")
                    results.append(True)
                else:
                    print(f"   Status: ❌ FAIL (Expected abuse={test_case['expected_abuse']}, got {is_abuse})")
                    results.append(False)
                    
            except Exception as e:
                print(f"   Status: ❌ ERROR - {str(e)}")
                results.append(False)
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 Test Summary")
        print(f"Total tests: {len(results)}")
        print(f"Passed: {sum(results)}")
        print(f"Failed: {len(results) - sum(results)}")
        
        if all(results):
            print("🎉 All tests passed!")
        else:
            print("⚠️  Some tests failed. Check the implementation.")
        
        return all(results)


def test_ip_stats():
    """Test the IP statistics functionality"""
    
    print("\n🔍 Testing IP Statistics Functionality")
    print("=" * 50)
    
    app = create_app('development')
    
    with app.app_context():
        # Test with a few sample IPs
        test_ips = ['127.0.0.1', '***********', 'unknown']
        
        for ip in test_ips:
            print(f"\n📊 Stats for IP: {ip}")
            try:
                stats = PromoAbuseDetector.get_ip_promo_usage_stats(ip)
                
                print(f"   Total users: {stats.get('total_users_from_ip', 0)}")
                print(f"   Users with promos: {stats.get('users_with_promos', 0)}")
                print(f"   TCFCA usage count: {stats.get('tcfca_usage_count', 0)}")
                print(f"   Is suspicious: {stats.get('is_suspicious', False)}")
                
                if 'error' in stats:
                    print(f"   Error: {stats['error']}")
                    
            except Exception as e:
                print(f"   Error: {str(e)}")


def main():
    """Run all tests"""
    print("🚀 Starting Promo Abuse Detection Tests")
    
    try:
        # Test abuse detection
        detection_passed = test_promo_abuse_detection()
        
        # Test IP stats
        test_ip_stats()
        
        print("\n" + "=" * 60)
        if detection_passed:
            print("✅ Promo abuse detection system is working correctly!")
        else:
            print("❌ Promo abuse detection system has issues that need to be fixed.")
            
        print("\n💡 Next steps:")
        print("1. Update your database schema with the new IP tracking fields")
        print("2. Test with real user data in your development environment")
        print("3. Monitor the admin endpoints for suspicious activity")
        print("4. Consider adding rate limiting for promo code attempts")
        
    except Exception as e:
        print(f"❌ Test execution failed: {str(e)}")
        return False
    
    return detection_passed


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
