from flask import Blueprint, jsonify, request, current_app, session
import stripe
from datetime import datetime, timezone, timedelta
from . import auth

payments_bp = Blueprint('payments', __name__, url_prefix='/api/payments')

# Use the function from auth module
login_required = auth.login_required

@payments_bp.route('/membership-status', methods=['GET'])
@login_required
def get_membership_status():
    """Get current user's membership status"""
    result = current_app.supabase.table('users').select('*').eq('id', session['user_id']).execute()
    user = result.data[0] if result.data else None
    
    if not user:
        return jsonify({'error': 'User not found'}), 404
    
    membership_status = None
    
    # Check for lifetime membership: paid/premium user with no expiration date
    if user.get('membership_type', 'free') in ['premium', 'paid'] and user.get('membership_expires_at') is None:
        membership_status = {
            'active': True,
            'lifetime': True,
            'type': 'lifetime'
        }
    elif user.get('membership_type', 'free') in ['premium', 'paid']:
        expires = user.get('membership_expires_at')
        if isinstance(expires, str):
            try:
                expires_dt = datetime.fromisoformat(expires)
                if expires_dt.tzinfo is None:
                    expires_dt = expires_dt.replace(tzinfo=timezone.utc)
            except Exception:
                expires_dt = None
        else:
            expires_dt = expires
        
        if expires_dt and expires_dt > datetime.now(timezone.utc):
            remaining_days = (expires_dt - datetime.now(timezone.utc)).days
            membership_status = {
                'active': True,
                'lifetime': False,
                'type': 'time_limited',
                'remaining_days': remaining_days,
                'expires_date': expires_dt.strftime('%d/%m/%Y'),
                'expires_iso': expires_dt.isoformat()
            }
        else:
            membership_status = {
                'active': False,
                'expired': True,
                'type': 'expired'
            }
    else:
        membership_status = {
            'active': False,
            'type': 'none'
        }
    
    return jsonify({
        'membership_status': membership_status,
        'user': {
            'id': user['id'],
            'username': user['username'],
            'email': user['email'],
            'membership_type': 'lifetime' if (user.get('membership_type') in ['premium', 'paid'] and user.get('membership_expires_at') is None) else user.get('membership_type', 'free'),
            'membership_expires_at': user.get('membership_expires_at')
        }
    })

@payments_bp.route('/create-checkout-session', methods=['POST'])
@login_required
def create_checkout_session():
    """Create Stripe checkout session"""
    # Check if Stripe is properly configured
    if not hasattr(stripe, 'api_key') or not stripe.api_key or stripe.api_key == 'sk_test_placeholder':
        print("ERROR: Stripe is not properly configured")
        return jsonify({'error': 'Service de paiement non configuré. Veuillez contacter le support.'}), 503
    
    data = request.get_json()
    plan = data.get('plan')
    
    # Map plan to price and description
    plans = {
        '1w': {'amount': 699, 'desc': 'Abonnement 1 semaine'},
        '1m': {'amount': 1399, 'desc': 'Abonnement 1 mois'},
        '3m': {'amount': 2699, 'desc': 'Abonnement 3 mois'},
        '12m': {'amount': 5499, 'desc': 'Abonnement 12 mois'},
    }
    
    if plan not in plans:
        return jsonify({'error': 'Plan invalide'}), 400
    
    try:
        print(f'Creating Stripe checkout session with:')
        print(f'  - User ID: {session["user_id"]}')
        print(f'  - Plan: {plan}')
        print(f'  - Amount: {plans[plan]["amount"]}')
        print(f'  - Currency: cad')
        print(f'  - Success URL: {current_app.config.get("FRONTEND_URL")}/payment/success')
        print(f'  - Cancel URL: {current_app.config.get("FRONTEND_URL")}/payment/cancel')
        
        stripe_session = stripe.checkout.Session.create(
            payment_method_types=['card', 'alipay'],
            payment_method_options={
                'card': {
                    'request_three_d_secure': 'automatic'
                }
            },
            line_items=[{
                'price_data': {
                    'currency': 'cad',
                    'unit_amount': plans[plan]['amount'],
                    'product_data': {
                        'name': plans[plan]['desc'],
                    },
                },
                'quantity': 1,
            }],
            mode='payment',
            success_url=f"{current_app.config.get('FRONTEND_URL')}/payment/success?session_id={{CHECKOUT_SESSION_ID}}",
            cancel_url=f"{current_app.config.get('FRONTEND_URL')}/payment/cancel",
            metadata={
                'user_id': session['user_id'],
                'plan': plan
            }
        )
        print(f'SUCCESS: Stripe session created with ID: {stripe_session.id}')
        return jsonify({'session_id': stripe_session.id})
        
    except stripe.error.CardError:
        return jsonify({'error': 'Carte refusée. Veuillez vérifier vos informations.'}), 400
    except stripe.error.RateLimitError:
        return jsonify({'error': 'Trop de tentatives. Veuillez réessayer dans quelques minutes.'}), 429
    except stripe.error.InvalidRequestError:
        return jsonify({'error': 'Erreur de configuration. Veuillez contacter le support.'}), 400
    except stripe.error.AuthenticationError:
        return jsonify({'error': 'Erreur de configuration. Veuillez contacter le support.'}), 500
    except stripe.error.APIConnectionError:
        return jsonify({'error': 'Erreur de connexion. Veuillez réessayer.'}), 503
    except stripe.error.StripeError:
        return jsonify({'error': 'Erreur de paiement. Veuillez réessayer.'}), 500
    except Exception as e:
        print(f'Unexpected error: {e}')
        return jsonify({'error': 'Erreur technique. Veuillez réessayer plus tard.'}), 500

@payments_bp.route('/webhook', methods=['POST'])
def stripe_webhook():
    """Handle Stripe webhook events"""
    payload = request.data
    sig_header = request.headers.get('stripe-signature')
    webhook_secret = current_app.config['STRIPE_WEBHOOK_SECRET']
    event = None

    print(f'Webhook called - payload size: {len(payload)} bytes')
    print(f'Signature header present: {bool(sig_header)}')
    print(f'Webhook secret configured: {bool(webhook_secret)}')
    print(f'Webhook secret (first 20 chars): {webhook_secret[:20] if webhook_secret else "None"}...')

    if not sig_header:
        print('ERROR: Missing stripe-signature header')
        return jsonify({'error': 'Missing stripe-signature header'}), 400

    # Check if Supabase is available
    if not current_app.supabase:
        print('ERROR: Supabase connection not available')
        return jsonify({'error': 'Database connection not available'}), 500

    # For development: if webhook secret is placeholder, skip verification temporarily
    if webhook_secret == 'whsec_placeholder':
        print('WARNING: Using placeholder webhook secret - skipping signature verification for development')
        try:
            import json
            event = json.loads(payload.decode('utf-8'))
            print(f'Event parsed successfully (dev mode): {event["type"]}')
        except Exception as e:
            print(f'ERROR: Failed to parse payload in dev mode: {e}')
            return jsonify({'error': 'Invalid payload'}), 400
    else:
        # Production mode: verify signature
        try:
            event = stripe.Webhook.construct_event(
                payload, sig_header, webhook_secret
            )
            current_app.logger.info(f'Event verified successfully: {event["type"]}')
        except ValueError as e:
            current_app.logger.error(f'Invalid payload: {e}')
            return jsonify({'error': 'Invalid payload'}), 400
        except stripe.error.SignatureVerificationError as e:
            current_app.logger.error(f'Invalid signature: {e}')
            # In development, log but continue (remove this in production)
            if current_app.config.get('DEBUG', False):
                current_app.logger.warning('DEBUG MODE: Continuing despite signature error for testing')
                try:
                    import json
                    event = json.loads(payload.decode('utf-8'))
                    current_app.logger.info(f'Event parsed in debug mode: {event["type"]}')
                except Exception as parse_error:
                    current_app.logger.error(f'Failed to parse payload: {parse_error}')
                    return jsonify({'error': 'Invalid signature and payload'}), 400
            else:
                return jsonify({'error': 'Invalid signature'}), 400
        except Exception as e:
            print(f'ERROR: Webhook verification failed - {e}')
            return jsonify({'error': 'Webhook error'}), 400

    # Handle the checkout.session.completed event
    if event['type'] == 'checkout.session.completed':
        session_data = event['data']['object']
        print(f'Processing checkout.session.completed: {session_data["id"]}')
        
        # Retrieve user_id and plan from metadata
        user_id = session_data.get('metadata', {}).get('user_id')
        plan = session_data.get('metadata', {}).get('plan')
        payment_status = session_data.get('payment_status')
        
        print(f'User: {user_id}, Plan: {plan}, Payment Status: {payment_status}')
        
        if user_id and plan and payment_status == 'paid':
            try:
                # Calculate new expiration date
                now = datetime.now(timezone.utc)
                result = current_app.supabase.table('users').select('*').eq('id', user_id).execute()
                user = result.data[0] if result.data else None
                
                if not user:
                    print(f'ERROR: User {user_id} not found in database')
                    return jsonify({'error': 'User not found'}), 404
                
                print(f'User found: {user.get("username", "unknown")}')
                
                if plan == '1w':
                    # Check if user has existing membership
                    current_expires = user.get('membership_expires_at') if user else None
                    if current_expires and user.get('membership_type', 'none') in ['premium', 'paid']:
                        if isinstance(current_expires, str):
                            try:
                                current_expires_dt = datetime.fromisoformat(current_expires)
                                if current_expires_dt.tzinfo is None:
                                    current_expires_dt = current_expires_dt.replace(tzinfo=timezone.utc)
                            except Exception:
                                current_expires_dt = now
                        else:
                            current_expires_dt = current_expires
                        # Extend from current expiration date (or now if expired)
                        base_date = max(current_expires_dt, now)
                        expires = base_date + timedelta(days=7)
                    else:
                        expires = now + timedelta(days=7)
                elif plan == '1m':
                    current_expires = user.get('membership_expires_at') if user else None
                    if current_expires and user.get('membership_type', 'none') in ['premium', 'paid']:
                        if isinstance(current_expires, str):
                            try:
                                current_expires_dt = datetime.fromisoformat(current_expires)
                                if current_expires_dt.tzinfo is None:
                                    current_expires_dt = current_expires_dt.replace(tzinfo=timezone.utc)
                            except Exception:
                                current_expires_dt = now
                        else:
                            current_expires_dt = current_expires
                        base_date = max(current_expires_dt, now)
                        expires = base_date + timedelta(days=30)
                    else:
                        expires = now + timedelta(days=30)
                elif plan == '3m':
                    current_expires = user.get('membership_expires_at') if user else None
                    if current_expires and user.get('membership_type', 'none') in ['premium', 'paid']:
                        if isinstance(current_expires, str):
                            try:
                                current_expires_dt = datetime.fromisoformat(current_expires)
                                if current_expires_dt.tzinfo is None:
                                    current_expires_dt = current_expires_dt.replace(tzinfo=timezone.utc)
                            except Exception:
                                current_expires_dt = now
                        else:
                            current_expires_dt = current_expires
                        base_date = max(current_expires_dt, now)
                        expires = base_date + timedelta(days=90)
                    else:
                        expires = now + timedelta(days=90)
                elif plan == '6m':
                    current_expires = user.get('membership_expires_at') if user else None
                    if current_expires and user.get('membership_type', 'none') in ['premium', 'paid']:
                        if isinstance(current_expires, str):
                            try:
                                current_expires_dt = datetime.fromisoformat(current_expires)
                                if current_expires_dt.tzinfo is None:
                                    current_expires_dt = current_expires_dt.replace(tzinfo=timezone.utc)
                            except Exception:
                                current_expires_dt = now
                        else:
                            current_expires_dt = current_expires
                        base_date = max(current_expires_dt, now)
                        expires = base_date + timedelta(days=180)
                elif plan == '12m':
                    current_expires = user.get('membership_expires_at') if user else None
                    if current_expires and user.get('membership_type', 'none') in ['premium', 'paid']:
                        if isinstance(current_expires, str):
                            try:
                                current_expires_dt = datetime.fromisoformat(current_expires)
                                if current_expires_dt.tzinfo is None:
                                    current_expires_dt = current_expires_dt.replace(tzinfo=timezone.utc)
                            except Exception:
                                current_expires_dt = now
                        else:
                            current_expires_dt = current_expires
                        base_date = max(current_expires_dt, now)
                        expires = base_date + timedelta(days=365)
                    else:
                        expires = now + timedelta(days=365)
                else:
                    print(f'ERROR: Unknown plan: {plan}')
                    return jsonify({'error': 'Unknown plan'}), 400
                
                # Set membership type based on plan
                membership_type = 'premium'
                update = {'membership_type': membership_type}
                if expires:
                    update['membership_expires_at'] = expires.isoformat()
                    print(f'Setting membership to expire at: {expires.isoformat()}')
                else:
                    update['membership_expires_at'] = None
                    print('Setting membership with no expiration (should not happen)')
                
                result = current_app.supabase.table('users').update(update).eq('id', user_id).execute()
                
                if result.data:
                    print(f'SUCCESS: Membership updated successfully for user {user_id}')
                else:
                    print(f'ERROR: Failed to update membership for user {user_id} - no data returned')
                    return jsonify({'error': 'Database update failed'}), 500
                    
            except Exception as e:
                print(f'ERROR: Exception updating membership: {e}')
                import traceback
                traceback.print_exc()
                return jsonify({'error': 'Database error'}), 500
        else:
            print(f'WARNING: Missing data or payment not completed: user_id={user_id}, plan={plan}, payment_status={payment_status}')
    
    elif event['type'] == 'checkout.session.expired':
        session_data = event['data']['object']
        print(f'Checkout session expired: {session_data["id"]}')
    
    elif event['type'] == 'payment_intent.payment_failed':
        payment_intent = event['data']['object']
        print(f'Payment failed: {payment_intent["id"]}, reason: {payment_intent.get("last_payment_error", {}).get("message", "Unknown")}')
    
    else:
        print(f'Unhandled event type: {event["type"]}')
    
    print('Webhook processed successfully')
    return jsonify({'status': 'success'}), 200

@payments_bp.route('/promo/redeem', methods=['POST'])
@login_required
def redeem_promo():
    """Redeem a promo code"""
    try:
        data = request.get_json()
        print(f"Received promo redemption request: {data}")
        
        promo_code = data.get('promo_code', '').strip() if data else ''
        print(f"Extracted promo_code: '{promo_code}'")
        
        # Validate promo code input
        if not promo_code:
            print("Error: Empty promo code")
            return jsonify({'error': 'Veuillez entrer un code promo.'}), 400
        
        if len(promo_code) < 3:
            print(f"Error: Promo code too short: {len(promo_code)} characters")
            return jsonify({'error': 'Le code promo doit contenir au moins 3 caractères.'}), 400
        
        if len(promo_code) > 20:
            print(f"Error: Promo code too long: {len(promo_code)} characters")
            return jsonify({'error': 'Le code promo ne peut pas dépasser 20 caractères.'}), 400
        
        # Check for invalid characters
        if not promo_code.replace('-', '').replace('_', '').isalnum():
            print(f"Error: Invalid characters in promo code: '{promo_code}'")
            return jsonify({'error': 'Le code promo ne peut contenir que des lettres, chiffres, tirets et underscores.'}), 400
        
        promo_code = promo_code.upper()
        print(f"Normalized promo_code: '{promo_code}'")
        
        # Get current user
        print(f"Getting user with ID: {session.get('user_id')}")
        result = current_app.supabase.table('users').select('*').eq('id', session['user_id']).execute()
        user = result.data[0] if result.data else None
        print(f"User found: {user is not None}")
        
        if not user:
            print("Error: User not found")
            return jsonify({'error': 'Utilisateur non trouvé.'}), 404
        
        print(f"User membership: {user.get('membership_type')}, expires: {user.get('membership_expires_at')}")
        
        # Check if user has lifetime membership (block promo codes for lifetime users only)
        if user.get('membership_type') == 'lifetime':
            print("Error: User has lifetime membership")
            return jsonify({'error': 'Vous avez déjà un abonnement à vie. Vous ne pouvez pas utiliser de code promo.'}), 400
        
        # Check if user has already used this promo code
        used_promo_codes = user.get('used_promo_codes', []) or []
        if promo_code in used_promo_codes:
            print(f"Error: User has already used promo code '{promo_code}'")
            return jsonify({'error': f'Vous avez déjà utilisé le code promo "{promo_code}". Chaque code ne peut être utilisé qu\'une seule fois par utilisateur.'}), 400

        # IP-based abuse detection for specific promo codes (like 'tcfca')
        from ..utils.promo_abuse_detector import PromoAbuseDetector
        from ..security import get_client_ip

        client_ip = get_client_ip()
        is_abuse, abuse_reason = PromoAbuseDetector.check_ip_abuse_for_promo(
            client_ip, promo_code, session['user_id']
        )

        if is_abuse:
            # Log the abuse attempt
            PromoAbuseDetector.log_abuse_attempt(
                client_ip, promo_code, session['user_id'],
                user.get('username', 'Unknown'), abuse_reason
            )

            print(f"Error: Promo abuse detected - {abuse_reason}")
            return jsonify({
                'error': f'Ce code promo a déjà été utilisé depuis cette adresse IP. '
                        f'Chaque code promo ne peut être utilisé qu\'une seule fois par adresse IP pour éviter les abus.'
            }), 400
        
        # Check if user has reached the maximum number of promo codes (3)
        if len(used_promo_codes) >= 3:
            print(f"Error: User has reached maximum promo codes: {len(used_promo_codes)}/3")
            return jsonify({'error': 'Vous avez atteint la limite de 3 codes promo par compte. Vous ne pouvez plus utiliser de nouveaux codes.'}), 400
        
        # Check if promo code exists
        print(f"Looking for promo code: '{promo_code}'")
        promo_result = current_app.supabase.table('promo_codes').select('*').eq('code', promo_code).execute()
        promo = promo_result.data[0] if promo_result.data else None
        print(f"Promo code found: {promo is not None}")
        
        if promo:
            print(f"Promo details: is_active={promo.get('is_active')}, max_uses={promo.get('max_uses')}, current_uses={promo.get('current_uses')}, membership_duration={promo.get('membership_duration')}")
            print(f"Full promo data: {promo}")
        
        if not promo:
            print(f"Error: Promo code '{promo_code}' not found")
            return jsonify({'error': f'Le code promo "{promo_code}" n\'existe pas. Vérifiez l\'orthographe.'}), 404
        
        # Check if promo code is active (using is_active column name)
        if not promo.get('is_active', False):
            print(f"Error: Promo code '{promo_code}' is not active: {promo.get('is_active')}")
            return jsonify({'error': f'Le code promo "{promo_code}" a été désactivé et ne peut plus être utilisé.'}), 400
        
        # Check if promo code has remaining uses
        max_uses = promo.get('max_uses', 0)
        current_uses = promo.get('current_uses', 0)
        uses_remaining = max_uses - current_uses
        
        if uses_remaining <= 0:
            print(f"Error: Promo code '{promo_code}' has no uses remaining: {current_uses}/{max_uses}")
            return jsonify({'error': f'Le code promo "{promo_code}" a été épuisé. Tous les usages ont été consommés.'}), 400
        
        # Apply the promo code
        print("Applying promo code...")
        now = datetime.now(timezone.utc)
        membership_days = promo.get('membership_duration', 3)
        
        # Calculate new expiration date based on existing membership
        current_expires = user.get('membership_expires_at')
        if current_expires and user.get('membership_type') in ['premium', 'paid']:
            # User has existing limited membership - extend it
            if isinstance(current_expires, str):
                try:
                    current_expires_dt = datetime.fromisoformat(current_expires)
                    if current_expires_dt.tzinfo is None:
                        current_expires_dt = current_expires_dt.replace(tzinfo=timezone.utc)
                except Exception:
                    current_expires_dt = now
            else:
                current_expires_dt = current_expires
            
            # Extend from current expiration date (or now if expired)
            base_date = max(current_expires_dt, now)
            expires = base_date + timedelta(days=membership_days)
            action_message = "prolongé"
        else:
            # User has no membership or expired membership - start fresh
            expires = now + timedelta(days=membership_days)
            action_message = "activé"
        
        print(f"New expiration: {expires.isoformat()}")
        
        # Update user's used_promo_codes array
        updated_used_codes = used_promo_codes + [promo_code]
        
        # Update user membership and used promo codes
        update_result = current_app.supabase.table('users').update({
            'membership_type': 'premium',
            'membership_expires_at': expires.isoformat(),
            'used_promo_codes': updated_used_codes
        }).eq('id', session['user_id']).execute()
        
        print(f"User update result: {update_result.data is not None}")
        
        if not update_result.data:
            print("Error: Failed to update user")
            return jsonify({'error': 'Erreur lors de la mise à jour de votre compte. Veuillez réessayer.'}), 500
        
        # Log the promo usage in promo_usage_log table (optional - won't fail if table doesn't exist)
        try:
            usage_log_result = current_app.supabase.table('promo_usage_log').insert({
                'user_id': session['user_id'],
                'username': user.get('username'),
                'promo_code': promo_code,
                'used_at': now.isoformat(),
                'expires_at': expires.isoformat(),
                'membership_days': membership_days,
                'remaining_uses_after': uses_remaining - 1
            }).execute()
            
            print(f"Usage log result: {usage_log_result.data is not None}")
            
            if not usage_log_result.data:
                print("Warning: Failed to log promo usage, but continuing...")
        except Exception as log_error:
            print(f"Warning: Failed to log promo usage (table may not exist): {log_error}")
            # Don't fail the whole operation if logging fails - table might be deleted
        
        # Decrement promo code uses
        promo_update_result = current_app.supabase.table('promo_codes').update({
            'current_uses': current_uses + 1
        }).eq('id', promo['id']).execute()
        
        print(f"Promo update result: {promo_update_result.data is not None}")
        
        if not promo_update_result.data:
            # Rollback user update if promo update fails
            print("Rolling back user update due to promo update failure")
            current_app.supabase.table('users').update({
                'membership_type': user.get('membership_type', 'none'),
                'membership_expires_at': user.get('membership_expires_at'),
                'used_promo_codes': used_promo_codes  # Rollback to original codes
            }).eq('id', session['user_id']).execute()
            return jsonify({'error': 'Erreur lors de la mise à jour du code promo. Veuillez réessayer.'}), 500
        
        # Success response
        remaining_uses = max_uses - (current_uses + 1)
        
        success_message = f'🎉 Code promo "{promo_code}" appliqué avec succès! Votre abonnement a été {action_message} de {membership_days} jour{"s" if membership_days > 1 else ""}.'
        print(f"Success: {success_message}")
        
        return jsonify({
            'message': success_message,
            'membership_expires_at': expires.isoformat(),
            'membership_days_added': membership_days,
            'action': action_message,
            'used_promo_codes_count': len(updated_used_codes)
        })
        
    except Exception as e:
        current_app.logger.error(f"Exception in redeem_promo: {e}")
        return jsonify({'error': 'Erreur technique. Veuillez réessayer plus tard.'}), 500

