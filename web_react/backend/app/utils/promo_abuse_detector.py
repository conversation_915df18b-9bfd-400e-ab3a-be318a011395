"""
Promo Code Abuse Detection Module

This module provides functionality to detect and prevent abuse of promotional codes,
specifically focusing on IP-based detection for the 'tcfca' promo code.
"""

from flask import current_app
from datetime import datetime, timezone
from typing import Dict, List, Optional, Tuple


class PromoAbuseDetector:
    """
    Detects and prevents promotional code abuse using various strategies.
    
    Currently focuses on IP-based detection for the 'tcfca' promo code to prevent
    users from creating multiple accounts from the same IP to abuse the 3-day trial.
    """
    
    # Promo codes that require IP-based abuse detection
    IP_RESTRICTED_PROMOS = {'TCFCA'}
    
    @staticmethod
    def check_ip_abuse_for_promo(client_ip: str, promo_code: str, current_user_id: str = None) -> Tuple[bool, str]:
        """
        Check if the given IP address has already been used for the specified promo code.
        
        Args:
            client_ip: The IP address to check
            promo_code: The promo code being used (case-insensitive)
            current_user_id: ID of the current user (to exclude from checks)
            
        Returns:
            Tuple of (is_abuse_detected: bool, reason: str)
        """
        if not client_ip or client_ip == 'unknown':
            return False, "IP address not available"
            
        promo_upper = promo_code.upper()
        
        # Only apply IP restrictions to specific promo codes
        if promo_upper not in PromoAbuseDetector.IP_RESTRICTED_PROMOS:
            return False, f"Promo code {promo_upper} not subject to IP restrictions"
        
        try:
            # Method 1: Check promo_usage_log for this IP and promo code
            abuse_detected, reason = PromoAbuseDetector._check_promo_usage_log(
                client_ip, promo_upper, current_user_id
            )
            
            if abuse_detected:
                return True, reason
            
            # Method 2: Check users table for accounts registered from this IP that used this promo
            abuse_detected, reason = PromoAbuseDetector._check_users_with_promo(
                client_ip, promo_upper, current_user_id
            )
            
            if abuse_detected:
                return True, reason
                
            return False, "No abuse detected"
            
        except Exception as e:
            current_app.logger.error(f"Error checking promo abuse for IP {client_ip}: {str(e)}")
            # In case of error, allow the promo to proceed (fail open)
            return False, f"Error during abuse check: {str(e)}"
    
    @staticmethod
    def _check_promo_usage_log(client_ip: str, promo_code: str, current_user_id: str = None) -> Tuple[bool, str]:
        """
        Check the promo_usage_log table for previous usage from this IP.
        
        This method cross-references the promo usage log with user IP addresses.
        """
        try:
            # Get all users who used this promo code
            promo_usage_result = current_app.supabase.table('promo_usage_log').select(
                'user_id, username, used_at'
            ).eq('promo_code', promo_code).execute()
            
            if not promo_usage_result.data:
                return False, "No previous usage found in log"
            
            # Get user IDs who used this promo
            user_ids_with_promo = [usage['user_id'] for usage in promo_usage_result.data]
            
            # Exclude current user if provided
            if current_user_id:
                user_ids_with_promo = [uid for uid in user_ids_with_promo if uid != current_user_id]
            
            if not user_ids_with_promo:
                return False, "No other users found with this promo"
            
            # Check if any of these users have the same IP address
            users_result = current_app.supabase.table('users').select(
                'id, username, registration_ip, last_ip'
            ).in_('id', user_ids_with_promo).execute()
            
            if not users_result.data:
                return False, "No user data found for promo users"
            
            # Check for IP matches
            for user in users_result.data:
                user_reg_ip = user.get('registration_ip')
                user_last_ip = user.get('last_ip')
                
                if user_reg_ip == client_ip or user_last_ip == client_ip:
                    username = user.get('username', 'Unknown')
                    return True, f"IP {client_ip} already used {promo_code} by user {username}"
            
            return False, "No IP matches found in usage log"
            
        except Exception as e:
            current_app.logger.error(f"Error checking promo usage log: {str(e)}")
            raise
    
    @staticmethod
    def _check_users_with_promo(client_ip: str, promo_code: str, current_user_id: str = None) -> Tuple[bool, str]:
        """
        Check the users table for accounts from this IP that have used this promo.
        
        This method looks for users registered from the same IP who have the promo in their used_promo_codes.
        """
        try:
            # Build query to find users from this IP
            query = current_app.supabase.table('users').select(
                'id, username, registration_ip, last_ip, used_promo_codes'
            )
            
            # Use OR condition to check both registration_ip and last_ip
            # Note: Supabase doesn't support OR in a single query easily, so we'll do two queries
            
            # Query 1: Users registered from this IP
            reg_ip_result = query.eq('registration_ip', client_ip).execute()
            
            # Query 2: Users with this as last IP
            last_ip_result = query.eq('last_ip', client_ip).execute()
            
            # Combine results and deduplicate
            all_users = {}
            for user in (reg_ip_result.data or []) + (last_ip_result.data or []):
                all_users[user['id']] = user
            
            # Exclude current user if provided
            if current_user_id and current_user_id in all_users:
                del all_users[current_user_id]
            
            if not all_users:
                return False, "No other users found from this IP"
            
            # Check if any of these users have used the promo code
            for user in all_users.values():
                used_promos = user.get('used_promo_codes', []) or []
                if promo_code in used_promos:
                    username = user.get('username', 'Unknown')
                    return True, f"IP {client_ip} already used {promo_code} by user {username}"
            
            return False, "No users from this IP have used this promo"
            
        except Exception as e:
            current_app.logger.error(f"Error checking users with promo: {str(e)}")
            raise
    
    @staticmethod
    def log_abuse_attempt(client_ip: str, promo_code: str, user_id: str, username: str, reason: str):
        """
        Log a promo abuse attempt for monitoring and analysis.
        
        Args:
            client_ip: The IP address of the abuse attempt
            promo_code: The promo code that was attempted
            user_id: ID of the user attempting abuse
            username: Username of the user attempting abuse
            reason: Reason why it was flagged as abuse
        """
        try:
            current_app.logger.warning(
                f"🚨 PROMO ABUSE DETECTED - IP: {client_ip}, User: {username} ({user_id}), "
                f"Promo: {promo_code}, Reason: {reason}"
            )
            
            # Could also log to a dedicated abuse_log table if needed
            # For now, using application logs is sufficient
            
        except Exception as e:
            current_app.logger.error(f"Error logging abuse attempt: {str(e)}")
    
    @staticmethod
    def get_ip_promo_usage_stats(client_ip: str) -> Dict:
        """
        Get statistics about promo code usage from a specific IP address.
        
        Args:
            client_ip: The IP address to analyze
            
        Returns:
            Dictionary with usage statistics
        """
        try:
            # Get all users from this IP
            users_result = current_app.supabase.table('users').select(
                'id, username, registration_ip, last_ip, used_promo_codes, created_at'
            ).or_(f'registration_ip.eq.{client_ip},last_ip.eq.{client_ip}').execute()
            
            users = users_result.data or []
            
            # Analyze promo usage
            total_users = len(users)
            users_with_promos = 0
            all_used_promos = []
            tcfca_usage_count = 0
            
            for user in users:
                used_promos = user.get('used_promo_codes', []) or []
                if used_promos:
                    users_with_promos += 1
                    all_used_promos.extend(used_promos)
                    if 'TCFCA' in used_promos:
                        tcfca_usage_count += 1
            
            return {
                'ip_address': client_ip,
                'total_users_from_ip': total_users,
                'users_with_promos': users_with_promos,
                'total_promo_uses': len(all_used_promos),
                'unique_promos_used': list(set(all_used_promos)),
                'tcfca_usage_count': tcfca_usage_count,
                'is_suspicious': tcfca_usage_count > 1 or users_with_promos > 2
            }
            
        except Exception as e:
            current_app.logger.error(f"Error getting IP promo stats: {str(e)}")
            return {
                'ip_address': client_ip,
                'error': str(e)
            }
