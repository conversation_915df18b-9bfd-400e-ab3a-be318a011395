"""
Promo Code Abuse Detection Module

This module provides functionality to detect and prevent abuse of promotional codes,
specifically focusing on IP-based detection for the 'tcfca' promo code.

Uses the promo_codes.used_ips array to track which IP addresses have used each promo code.
"""

from flask import current_app
from datetime import datetime, timezone
from typing import Dict, List, Optional, Tuple


class PromoAbuseDetector:
    """
    Detects and prevents promotional code abuse using IP tracking in promo_codes table.

    Much simpler approach: each promo code has a used_ips array that tracks
    which IP addresses have already used that specific promo code.
    """

    # Promo codes that require IP-based abuse detection
    IP_RESTRICTED_PROMOS = {'TCFCA'}

    @staticmethod
    def check_ip_abuse_for_promo(client_ip: str, promo_code: str, current_user_id: str = None, user_data: dict = None) -> Tuple[bool, str]:
        """
        Check if any of the user's IP addresses have already been used for the specified promo code.
        Checks current client IP, registration IP, and last IP.

        Args:
            client_ip: The current request IP address
            promo_code: The promo code being used (case-insensitive)
            current_user_id: ID of the current user (not used in this simple approach)
            user_data: User data containing registration_ip and last_ip

        Returns:
            Tuple of (is_abuse_detected: bool, reason: str)
        """
        promo_upper = promo_code.upper()

        # Only apply IP restrictions to specific promo codes
        if promo_upper not in PromoAbuseDetector.IP_RESTRICTED_PROMOS:
            return False, f"Promo code {promo_upper} not subject to IP restrictions"

        # Collect all IPs to check
        ips_to_check = []

        # Add current client IP
        if client_ip and client_ip != 'unknown':
            ips_to_check.append(client_ip)

        # Add user's registration and last IP if available
        if user_data:
            registration_ip = user_data.get('registration_ip')
            last_ip = user_data.get('last_ip')

            if registration_ip and registration_ip != 'unknown':
                ips_to_check.append(registration_ip)

            if last_ip and last_ip != 'unknown' and last_ip != registration_ip:
                ips_to_check.append(last_ip)

        # Remove duplicates while preserving order
        ips_to_check = list(dict.fromkeys(ips_to_check))

        if not ips_to_check:
            return False, "No valid IP addresses to check"

        try:
            # Get the promo code record
            promo_result = current_app.supabase.table('promo_codes').select(
                'id, code, used_ips'
            ).eq('code', promo_upper).execute()

            if not promo_result.data:
                return False, f"Promo code {promo_upper} not found"

            promo = promo_result.data[0]
            used_ips = promo.get('used_ips', []) or []

            # Check if any of the user's IPs have already used this promo code
            for ip in ips_to_check:
                if ip in used_ips:
                    return True, f"Promo code already used from IP address {ip}"

            return False, "None of the user's IP addresses have used this promo code before"

        except Exception as e:
            current_app.logger.error(f"Error checking promo abuse for IP {client_ip}: {str(e)}")
            # In case of error, allow the promo to proceed (fail open)
            return False, f"Error during abuse check: {str(e)}"

    @staticmethod
    def record_ip_usage(client_ip: str, promo_code: str, user_data: dict = None) -> bool:
        """
        Record that IP addresses have used a specific promo code.
        Records the current client IP, plus user's registration_ip and last_ip if available.

        Args:
            client_ip: The current request IP address
            promo_code: The promo code that was used
            user_data: User data containing registration_ip and last_ip

        Returns:
            bool: True if successfully recorded, False otherwise
        """
        promo_upper = promo_code.upper()

        # Only record for IP-restricted promos
        if promo_upper not in PromoAbuseDetector.IP_RESTRICTED_PROMOS:
            return True  # Not an error, just not tracked

        # Collect all IPs to record
        ips_to_record = []

        # Add current client IP
        if client_ip and client_ip != 'unknown':
            ips_to_record.append(client_ip)

        # Add user's registration and last IP if available
        if user_data:
            registration_ip = user_data.get('registration_ip')
            last_ip = user_data.get('last_ip')

            if registration_ip and registration_ip != 'unknown':
                ips_to_record.append(registration_ip)

            if last_ip and last_ip != 'unknown' and last_ip != registration_ip:
                ips_to_record.append(last_ip)

        # Remove duplicates while preserving order
        ips_to_record = list(dict.fromkeys(ips_to_record))

        if not ips_to_record:
            current_app.logger.warning("No valid IPs to record for promo usage")
            return False

        try:
            # Get current used_ips array
            promo_result = current_app.supabase.table('promo_codes').select(
                'id, used_ips'
            ).eq('code', promo_upper).execute()

            if not promo_result.data:
                current_app.logger.error(f"Cannot record IP usage: promo code {promo_upper} not found")
                return False

            promo = promo_result.data[0]
            used_ips = promo.get('used_ips', []) or []

            # Add new IPs that aren't already present
            new_ips_added = []
            for ip in ips_to_record:
                if ip not in used_ips:
                    used_ips.append(ip)
                    new_ips_added.append(ip)

            # Update the promo code record if we have new IPs to add
            if new_ips_added:
                update_result = current_app.supabase.table('promo_codes').update({
                    'used_ips': used_ips
                }).eq('id', promo['id']).execute()

                if update_result.data:
                    current_app.logger.info(f"Recorded IPs {new_ips_added} usage for promo {promo_upper}")
                    return True
                else:
                    current_app.logger.error(f"Failed to update used_ips for promo {promo_upper}")
                    return False
            else:
                current_app.logger.info(f"All IPs {ips_to_record} already recorded for promo {promo_upper}")
                return True

        except Exception as e:
            current_app.logger.error(f"Error recording IP usage: {str(e)}")
            return False


    @staticmethod
    def log_abuse_attempt(client_ip: str, promo_code: str, user_id: str, username: str, reason: str):
        """
        Log a promo abuse attempt for monitoring and analysis.

        Args:
            client_ip: The IP address of the abuse attempt
            promo_code: The promo code that was attempted
            user_id: ID of the user attempting abuse
            username: Username of the user attempting abuse
            reason: Reason why it was flagged as abuse
        """
        try:
            current_app.logger.warning(
                f"🚨 PROMO ABUSE DETECTED - IP: {client_ip}, User: {username} ({user_id}), "
                f"Promo: {promo_code}, Reason: {reason}"
            )

        except Exception as e:
            current_app.logger.error(f"Error logging abuse attempt: {str(e)}")

    @staticmethod
    def get_promo_ip_stats(promo_code: str) -> Dict:
        """
        Get statistics about IP usage for a specific promo code.

        Args:
            promo_code: The promo code to analyze

        Returns:
            Dictionary with usage statistics
        """
        try:
            promo_upper = promo_code.upper()

            # Get the promo code record
            promo_result = current_app.supabase.table('promo_codes').select(
                'code, used_ips, current_uses, max_uses, is_active'
            ).eq('code', promo_upper).execute()

            if not promo_result.data:
                return {
                    'promo_code': promo_upper,
                    'error': 'Promo code not found'
                }

            promo = promo_result.data[0]
            used_ips = promo.get('used_ips', []) or []

            return {
                'promo_code': promo_upper,
                'total_ip_addresses': len(used_ips),
                'used_ips': used_ips,
                'current_uses': promo.get('current_uses', 0),
                'max_uses': promo.get('max_uses', 0),
                'is_active': promo.get('is_active', False),
                'is_ip_restricted': promo_upper in PromoAbuseDetector.IP_RESTRICTED_PROMOS
            }

        except Exception as e:
            current_app.logger.error(f"Error getting promo IP stats: {str(e)}")
            return {
                'promo_code': promo_code.upper(),
                'error': str(e)
            }
