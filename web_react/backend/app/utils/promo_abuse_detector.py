"""
Promo Code Abuse Detection Module

This module provides functionality to detect and prevent abuse of promotional codes,
specifically focusing on IP-based detection for the 'tcfca' promo code.

Uses the promo_codes.used_ips array to track which IP addresses have used each promo code.
"""

from flask import current_app
from datetime import datetime, timezone
from typing import Dict, List, Optional, Tuple


class PromoAbuseDetector:
    """
    Detects and prevents promotional code abuse using IP tracking in promo_codes table.

    Much simpler approach: each promo code has a used_ips array that tracks
    which IP addresses have already used that specific promo code.
    """

    # Promo codes that require IP-based abuse detection
    IP_RESTRICTED_PROMOS = {'TCFCA'}

    @staticmethod
    def check_ip_abuse_for_promo(client_ip: str, promo_code: str, current_user_id: str = None) -> Tuple[bool, str]:
        """
        Check if the given IP address has already been used for the specified promo code.

        Args:
            client_ip: The IP address to check
            promo_code: The promo code being used (case-insensitive)
            current_user_id: ID of the current user (not used in this simple approach)

        Returns:
            Tuple of (is_abuse_detected: bool, reason: str)
        """
        if not client_ip or client_ip == 'unknown':
            return False, "IP address not available"

        promo_upper = promo_code.upper()

        # Only apply IP restrictions to specific promo codes
        if promo_upper not in PromoAbuseDetector.IP_RESTRICTED_PROMOS:
            return False, f"Promo code {promo_upper} not subject to IP restrictions"

        try:
            # Get the promo code record
            promo_result = current_app.supabase.table('promo_codes').select(
                'id, code, used_ips'
            ).eq('code', promo_upper).execute()

            if not promo_result.data:
                return False, f"Promo code {promo_upper} not found"

            promo = promo_result.data[0]
            used_ips = promo.get('used_ips', []) or []

            # Check if this IP has already used this promo code
            if client_ip in used_ips:
                return True, "You have used this promo code before. If you believe this is an error, please contact support."

            return False, "IP has not used this promo code before"

        except Exception as e:
            current_app.logger.error(f"Error checking promo abuse for IP {client_ip}: {str(e)}")
            # In case of error, allow the promo to proceed (fail open)
            return False, f"Error during abuse check: {str(e)}"

    @staticmethod
    def record_ip_usage(client_ip: str, promo_code: str) -> bool:
        """
        Record that an IP address has used a specific promo code.

        Args:
            client_ip: The IP address that used the promo
            promo_code: The promo code that was used

        Returns:
            bool: True if successfully recorded, False otherwise
        """
        if not client_ip or client_ip == 'unknown':
            return False

        promo_upper = promo_code.upper()

        # Only record for IP-restricted promos
        if promo_upper not in PromoAbuseDetector.IP_RESTRICTED_PROMOS:
            return True  # Not an error, just not tracked

        try:
            # Get current used_ips array
            promo_result = current_app.supabase.table('promo_codes').select(
                'id, used_ips'
            ).eq('code', promo_upper).execute()

            if not promo_result.data:
                current_app.logger.error(f"Cannot record IP usage: promo code {promo_upper} not found")
                return False

            promo = promo_result.data[0]
            used_ips = promo.get('used_ips', []) or []

            # Add IP if not already present
            if client_ip not in used_ips:
                used_ips.append(client_ip)

                # Update the promo code record
                update_result = current_app.supabase.table('promo_codes').update({
                    'used_ips': used_ips
                }).eq('id', promo['id']).execute()

                if update_result.data:
                    current_app.logger.info(f"Recorded IP {client_ip} usage for promo {promo_upper}")
                    return True
                else:
                    current_app.logger.error(f"Failed to update used_ips for promo {promo_upper}")
                    return False
            else:
                # IP already recorded (shouldn't happen if abuse detection works)
                current_app.logger.warning(f"IP {client_ip} already recorded for promo {promo_upper}")
                return True

        except Exception as e:
            current_app.logger.error(f"Error recording IP usage: {str(e)}")
            return False


    @staticmethod
    def log_abuse_attempt(client_ip: str, promo_code: str, user_id: str, username: str, reason: str):
        """
        Log a promo abuse attempt for monitoring and analysis.

        Args:
            client_ip: The IP address of the abuse attempt
            promo_code: The promo code that was attempted
            user_id: ID of the user attempting abuse
            username: Username of the user attempting abuse
            reason: Reason why it was flagged as abuse
        """
        try:
            current_app.logger.warning(
                f"🚨 PROMO ABUSE DETECTED - IP: {client_ip}, User: {username} ({user_id}), "
                f"Promo: {promo_code}, Reason: {reason}"
            )

        except Exception as e:
            current_app.logger.error(f"Error logging abuse attempt: {str(e)}")

    @staticmethod
    def get_promo_ip_stats(promo_code: str) -> Dict:
        """
        Get statistics about IP usage for a specific promo code.

        Args:
            promo_code: The promo code to analyze

        Returns:
            Dictionary with usage statistics
        """
        try:
            promo_upper = promo_code.upper()

            # Get the promo code record
            promo_result = current_app.supabase.table('promo_codes').select(
                'code, used_ips, current_uses, max_uses, is_active'
            ).eq('code', promo_upper).execute()

            if not promo_result.data:
                return {
                    'promo_code': promo_upper,
                    'error': 'Promo code not found'
                }

            promo = promo_result.data[0]
            used_ips = promo.get('used_ips', []) or []

            return {
                'promo_code': promo_upper,
                'total_ip_addresses': len(used_ips),
                'used_ips': used_ips,
                'current_uses': promo.get('current_uses', 0),
                'max_uses': promo.get('max_uses', 0),
                'is_active': promo.get('is_active', False),
                'is_ip_restricted': promo_upper in PromoAbuseDetector.IP_RESTRICTED_PROMOS
            }

        except Exception as e:
            current_app.logger.error(f"Error getting promo IP stats: {str(e)}")
            return {
                'promo_code': promo_code.upper(),
                'error': str(e)
            }
