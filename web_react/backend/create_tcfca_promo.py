#!/usr/bin/env python3
"""
Create or update the TCFCA promo code with IP tracking support.

This script ensures the TCFCA promo code exists and has the proper structure
for IP-based abuse detection.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from datetime import datetime, timezone


def create_tcfca_promo():
    """Create or update the TCFCA promo code"""
    
    print("🎫 Creating/Updating TCFCA Promo Code")
    print("=" * 40)
    
    app = create_app('development')
    
    with app.app_context():
        try:
            # Check if TCFCA promo already exists
            existing_result = app.supabase.table('promo_codes').select('*').eq('code', 'TCFCA').execute()
            
            if existing_result.data:
                promo = existing_result.data[0]
                print(f"✅ TCFCA promo code already exists:")
                print(f"   ID: {promo.get('id')}")
                print(f"   Current uses: {promo.get('current_uses', 0)}")
                print(f"   Max uses: {promo.get('max_uses', 0)}")
                print(f"   Is active: {promo.get('is_active', False)}")
                print(f"   Used IPs: {len(promo.get('used_ips', []))} IPs")
                
                # Ensure used_ips field exists and is properly initialized
                if promo.get('used_ips') is None:
                    print("🔧 Updating promo to add used_ips field...")
                    update_result = app.supabase.table('promo_codes').update({
                        'used_ips': []
                    }).eq('id', promo['id']).execute()
                    
                    if update_result.data:
                        print("✅ Successfully added used_ips field")
                    else:
                        print("❌ Failed to add used_ips field")
                        return False
                
                return True
            
            else:
                # Create new TCFCA promo code
                print("🆕 Creating new TCFCA promo code...")
                
                promo_data = {
                    'code': 'TCFCA',
                    'is_active': True,
                    'max_uses': 1000,  # High limit for the trial promo
                    'current_uses': 0,
                    'membership_duration': 3,  # 3 days
                    'used_ips': [],  # Initialize empty IP tracking array
                    'created_at': datetime.now(timezone.utc).isoformat()
                }
                
                create_result = app.supabase.table('promo_codes').insert(promo_data).execute()
                
                if create_result.data:
                    promo = create_result.data[0]
                    print("✅ Successfully created TCFCA promo code:")
                    print(f"   ID: {promo.get('id')}")
                    print(f"   Code: {promo.get('code')}")
                    print(f"   Max uses: {promo.get('max_uses')}")
                    print(f"   Membership duration: {promo.get('membership_duration')} days")
                    return True
                else:
                    print("❌ Failed to create TCFCA promo code")
                    return False
                    
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            return False


def verify_promo_structure():
    """Verify that the promo code has the correct structure for abuse detection"""
    
    print("\n🔍 Verifying Promo Code Structure")
    print("=" * 40)
    
    app = create_app('development')
    
    with app.app_context():
        try:
            # Get the TCFCA promo
            result = app.supabase.table('promo_codes').select('*').eq('code', 'TCFCA').execute()
            
            if not result.data:
                print("❌ TCFCA promo code not found")
                return False
            
            promo = result.data[0]
            
            # Check required fields
            required_fields = ['id', 'code', 'is_active', 'max_uses', 'current_uses', 'used_ips']
            missing_fields = []
            
            for field in required_fields:
                if field not in promo or promo[field] is None:
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"❌ Missing required fields: {missing_fields}")
                return False
            
            # Verify used_ips is an array
            used_ips = promo.get('used_ips')
            if not isinstance(used_ips, list):
                print(f"❌ used_ips field is not an array: {type(used_ips)}")
                return False
            
            print("✅ TCFCA promo code structure is correct:")
            print(f"   Code: {promo['code']}")
            print(f"   Active: {promo['is_active']}")
            print(f"   Uses: {promo['current_uses']}/{promo['max_uses']}")
            print(f"   IP tracking: {len(used_ips)} IPs recorded")
            print(f"   Used IPs: {used_ips}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error verifying structure: {str(e)}")
            return False


def main():
    """Main function"""
    print("🚀 TCFCA Promo Code Setup")
    print("=" * 50)
    
    try:
        # Create/update the promo code
        if not create_tcfca_promo():
            print("❌ Failed to create/update TCFCA promo code")
            return False
        
        # Verify the structure
        if not verify_promo_structure():
            print("❌ Promo code structure verification failed")
            return False
        
        print("\n🎉 TCFCA promo code is ready for IP-based abuse detection!")
        print("\n💡 Next steps:")
        print("1. Run the database migration if you haven't already")
        print("2. Test the promo code with different IP addresses")
        print("3. Monitor usage through admin endpoints")
        
        return True
        
    except Exception as e:
        print(f"❌ Setup failed: {str(e)}")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
