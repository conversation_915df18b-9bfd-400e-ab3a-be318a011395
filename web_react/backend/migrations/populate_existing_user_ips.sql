-- =====================================================
-- Populate existing IP pools with registration_ip and last_ip
-- from users who have already used promo codes
-- =====================================================

-- Create a temporary function to add IPs to promo code pools
CREATE OR REPLACE FUNCTION add_user_ips_to_promo_pools()
RETURNS void AS $$
DECLARE
    user_record RECORD;
    promo_record RECORD;
    promo_code TEXT;
    current_used_ips TEXT[];
    new_ips_to_add TEXT[];
    ip_to_add TEXT;
    total_users_processed INTEGER := 0;
    total_ips_added INTEGER := 0;
BEGIN
    RAISE NOTICE 'Starting to populate existing IP pools with user registration_ip and last_ip...';
    
    -- Loop through all users who have used promo codes
    FOR user_record IN 
        SELECT id, username, registration_ip, last_ip, used_promo_codes
        FROM users 
        WHERE used_promo_codes IS NOT NULL 
        AND array_length(used_promo_codes, 1) > 0
    LOOP
        total_users_processed := total_users_processed + 1;
        
        RAISE NOTICE 'Processing user: % (ID: %)', user_record.username, user_record.id;
        RAISE NOTICE '  Registration IP: %', COALESCE(user_record.registration_ip, 'NULL');
        RAISE NOTICE '  Last IP: %', COALESCE(user_record.last_ip, 'NULL');
        RAISE NOTICE '  Used promo codes: %', array_to_string(user_record.used_promo_codes, ', ');
        
        -- Loop through each promo code this user has used
        FOREACH promo_code IN ARRAY user_record.used_promo_codes
        LOOP
            -- Only process IP-restricted promo codes (like TCFCA)
            IF UPPER(promo_code) IN ('TCFCA') THEN
                RAISE NOTICE '  Processing IP-restricted promo: %', promo_code;
                
                -- Get current used_ips for this promo code
                SELECT used_ips INTO current_used_ips
                FROM promo_codes 
                WHERE UPPER(code) = UPPER(promo_code);
                
                IF current_used_ips IS NULL THEN
                    current_used_ips := '{}';
                END IF;
                
                -- Prepare list of IPs to add
                new_ips_to_add := '{}';
                
                -- Add registration_ip if valid and not already present
                IF user_record.registration_ip IS NOT NULL 
                   AND user_record.registration_ip != 'unknown' 
                   AND user_record.registration_ip != '' 
                   AND NOT (user_record.registration_ip = ANY(current_used_ips)) THEN
                    new_ips_to_add := array_append(new_ips_to_add, user_record.registration_ip);
                    RAISE NOTICE '    Adding registration_ip: %', user_record.registration_ip;
                END IF;
                
                -- Add last_ip if valid, different from registration_ip, and not already present
                IF user_record.last_ip IS NOT NULL 
                   AND user_record.last_ip != 'unknown' 
                   AND user_record.last_ip != '' 
                   AND user_record.last_ip != user_record.registration_ip
                   AND NOT (user_record.last_ip = ANY(current_used_ips)) 
                   AND NOT (user_record.last_ip = ANY(new_ips_to_add)) THEN
                    new_ips_to_add := array_append(new_ips_to_add, user_record.last_ip);
                    RAISE NOTICE '    Adding last_ip: %', user_record.last_ip;
                END IF;
                
                -- Update the promo code with new IPs if any were found
                IF array_length(new_ips_to_add, 1) > 0 THEN
                    UPDATE promo_codes 
                    SET used_ips = current_used_ips || new_ips_to_add
                    WHERE UPPER(code) = UPPER(promo_code);
                    
                    total_ips_added := total_ips_added + array_length(new_ips_to_add, 1);
                    RAISE NOTICE '    Updated promo % with % new IPs', promo_code, array_length(new_ips_to_add, 1);
                ELSE
                    RAISE NOTICE '    No new IPs to add for promo %', promo_code;
                END IF;
            ELSE
                RAISE NOTICE '  Skipping non-IP-restricted promo: %', promo_code;
            END IF;
        END LOOP;
        
        RAISE NOTICE '  Finished processing user %', user_record.username;
        RAISE NOTICE '';
    END LOOP;
    
    RAISE NOTICE '=== SUMMARY ===';
    RAISE NOTICE 'Total users processed: %', total_users_processed;
    RAISE NOTICE 'Total IPs added to pools: %', total_ips_added;
    RAISE NOTICE 'Migration completed successfully!';
END;
$$ LANGUAGE plpgsql;

-- Execute the function
SELECT add_user_ips_to_promo_pools();

-- Drop the temporary function
DROP FUNCTION add_user_ips_to_promo_pools();

-- Show final state of IP-restricted promo codes
SELECT 
    code,
    array_length(used_ips, 1) as total_ips_in_pool,
    used_ips
FROM promo_codes 
WHERE UPPER(code) IN ('TCFCA')
ORDER BY code;

-- Show some statistics
WITH ip_stats AS (
    SELECT 
        code,
        unnest(used_ips) as ip_address
    FROM promo_codes 
    WHERE UPPER(code) IN ('TCFCA')
)
SELECT 
    code,
    COUNT(DISTINCT ip_address) as unique_ips,
    COUNT(ip_address) as total_ip_entries
FROM ip_stats
GROUP BY code
ORDER BY code;

RAISE NOTICE '';
RAISE NOTICE '🎉 IP pool population completed!';
RAISE NOTICE 'All registration_ip and last_ip values have been added to their corresponding promo code pools.';
RAISE NOTICE 'The anti-abuse system is now fully comprehensive!';
