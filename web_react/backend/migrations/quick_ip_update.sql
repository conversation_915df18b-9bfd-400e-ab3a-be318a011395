-- =====================================================
-- Quick one-liner to add all user IPs to TCFCA pool
-- =====================================================

-- Single UPDATE statement to add all registration_ip and last_ip 
-- from users who used TCFCA to the promo's IP pool
UPDATE promo_codes 
SET used_ips = array(
    SELECT DISTINCT unnest(
        used_ips || 
        COALESCE(array_agg(DISTINCT u.registration_ip) FILTER (WHERE u.registration_ip IS NOT NULL AND u.registration_ip != 'unknown' AND u.registration_ip != ''), '{}') ||
        COALESCE(array_agg(DISTINCT u.last_ip) FILTER (WHERE u.last_ip IS NOT NULL AND u.last_ip != 'unknown' AND u.last_ip != '' AND u.last_ip != u.registration_ip), '{}')
    )
    FROM users u 
    WHERE 'TCFCA' = ANY(u.used_promo_codes)
)
WHERE UPPER(code) = 'TCFCA';

-- Verify the update
SELECT 
    code,
    array_length(used_ips, 1) as total_ips,
    used_ips
FROM promo_codes 
WHERE UPPER(code) = 'TCFCA';
