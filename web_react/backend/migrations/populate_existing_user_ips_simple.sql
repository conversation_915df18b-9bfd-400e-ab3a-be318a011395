-- =====================================================
-- Simple SQL to populate existing IP pools with user IPs
-- Run this to add registration_ip and last_ip to promo pools
-- =====================================================

-- First, let's see the current state
SELECT 
    'BEFORE UPDATE' as status,
    code,
    array_length(used_ips, 1) as ip_count,
    used_ips
FROM promo_codes 
WHERE UPPER(code) = 'TCFCA';

-- Update TCFCA promo code with all registration_ip values from users who used it
UPDATE promo_codes 
SET used_ips = (
    SELECT DISTINCT array_agg(DISTINCT ip_address ORDER BY ip_address)
    FROM (
        -- Existing IPs in the pool
        SELECT unnest(used_ips) as ip_address
        FROM promo_codes 
        WHERE UPPER(code) = 'TCFCA'
        
        UNION
        
        -- Registration IPs from users who used TCFCA
        SELECT registration_ip as ip_address
        FROM users 
        WHERE 'TCFCA' = ANY(used_promo_codes)
        AND registration_ip IS NOT NULL 
        AND registration_ip != 'unknown'
        AND registration_ip != ''
        
        UNION
        
        -- Last IPs from users who used TCFCA  
        SELECT last_ip as ip_address
        FROM users 
        WHERE 'TCFCA' = ANY(used_promo_codes)
        AND last_ip IS NOT NULL 
        AND last_ip != 'unknown'
        AND last_ip != ''
    ) combined_ips
    WHERE ip_address IS NOT NULL
)
WHERE UPPER(code) = 'TCFCA';

-- Show the updated state
SELECT 
    'AFTER UPDATE' as status,
    code,
    array_length(used_ips, 1) as ip_count,
    used_ips
FROM promo_codes 
WHERE UPPER(code) = 'TCFCA';

-- Show detailed breakdown of IP sources
WITH ip_analysis AS (
    SELECT 
        'existing_pool' as source,
        unnest(used_ips) as ip_address
    FROM promo_codes 
    WHERE UPPER(code) = 'TCFCA'
    
    UNION ALL
    
    SELECT 
        'registration_ip' as source,
        registration_ip as ip_address
    FROM users 
    WHERE 'TCFCA' = ANY(used_promo_codes)
    AND registration_ip IS NOT NULL 
    AND registration_ip != 'unknown'
    AND registration_ip != ''
    
    UNION ALL
    
    SELECT 
        'last_ip' as source,
        last_ip as ip_address
    FROM users 
    WHERE 'TCFCA' = ANY(used_promo_codes)
    AND last_ip IS NOT NULL 
    AND last_ip != 'unknown'
    AND last_ip != ''
)
SELECT 
    source,
    COUNT(*) as ip_count,
    array_agg(DISTINCT ip_address ORDER BY ip_address) as ip_addresses
FROM ip_analysis
GROUP BY source
ORDER BY source;

-- Show users who used TCFCA and their IP information
SELECT 
    username,
    registration_ip,
    last_ip,
    CASE 
        WHEN registration_ip IS NOT NULL AND registration_ip != 'unknown' AND registration_ip != '' THEN 'YES'
        ELSE 'NO'
    END as reg_ip_added,
    CASE 
        WHEN last_ip IS NOT NULL AND last_ip != 'unknown' AND last_ip != '' AND last_ip != registration_ip THEN 'YES'
        ELSE 'NO'
    END as last_ip_added
FROM users 
WHERE 'TCFCA' = ANY(used_promo_codes)
ORDER BY username;
