-- Migration: Add IP tracking fields to users table
-- Date: 2025-07-30
-- Purpose: Add registration_ip and last_ip fields for promo abuse detection

-- Add IP tracking columns to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS registration_ip VARCHAR(45),
ADD COLUMN IF NOT EXISTS last_ip VARCHAR(45);

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_users_registration_ip ON users(registration_ip);
CREATE INDEX IF NOT EXISTS idx_users_last_ip ON users(last_ip);

-- Add comments for documentation
COMMENT ON COLUMN users.registration_ip IS 'IP address when user registered (supports IPv6)';
COMMENT ON COLUMN users.last_ip IS 'Last known IP address for security tracking';

-- Update existing users with 'unknown' IP if they don't have one
-- This is safe to run multiple times
UPDATE users 
SET registration_ip = 'unknown' 
WHERE registration_ip IS NULL;

UPDATE users 
SET last_ip = 'unknown' 
WHERE last_ip IS NULL;

-- Verify the migration
SELECT 
    COUNT(*) as total_users,
    COUNT(registration_ip) as users_with_reg_ip,
    COUNT(last_ip) as users_with_last_ip
FROM users;
