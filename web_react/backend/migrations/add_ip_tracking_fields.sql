-- Migration: Add IP tracking to promo_codes table
-- Date: 2025-07-30
-- Purpose: Track which IPs have used each promo code to prevent abuse

-- Add IP tracking column to promo_codes table
ALTER TABLE promo_codes
ADD COLUMN IF NOT EXISTS used_ips TEXT[] DEFAULT '{}';

-- Add index for better query performance on IP arrays
CREATE INDEX IF NOT EXISTS idx_promo_codes_used_ips ON promo_codes USING GIN(used_ips);

-- Add comment for documentation
COMMENT ON COLUMN promo_codes.used_ips IS 'Array of IP addresses that have used this promo code';

-- Initialize existing promo codes with empty IP arrays
-- This is safe to run multiple times
UPDATE promo_codes
SET used_ips = '{}'
WHERE used_ips IS NULL;

-- Verify the migration
SELECT
    code,
    current_uses,
    array_length(used_ips, 1) as ip_count,
    used_ips
FROM promo_codes
ORDER BY code;
