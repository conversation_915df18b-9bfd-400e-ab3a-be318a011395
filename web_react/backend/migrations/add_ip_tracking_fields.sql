-- Migration: Add IP tracking to promo_codes table
-- Date: 2025-07-30
-- Purpose: Track which IPs have used each promo code to prevent abuse

-- Add IP tracking column to promo_codes table
ALTER TABLE promo_codes
ADD COLUMN IF NOT EXISTS used_ips TEXT[] DEFAULT '{}';

-- Add index for better query performance on IP arrays
CREATE INDEX IF NOT EXISTS idx_promo_codes_used_ips ON promo_codes USING GIN(used_ips);

-- Add comment for documentation
COMMENT ON COLUMN promo_codes.used_ips IS 'Array of IP addresses that have used this promo code';

-- Initialize existing promo codes with empty IP arrays
-- This is safe to run multiple times
UPDATE promo_codes
SET used_ips = '{}'
WHERE used_ips IS NULL;

-- Populate existing IP data from users who have used promo codes
-- This finds users who have used promo codes and adds their IPs to the promo_codes.used_ips array

DO $$
DECLARE
    user_record RECORD;
    promo_code_text TEXT;
    user_ips TEXT[];
BEGIN
    -- Loop through all users who have used promo codes
    FOR user_record IN
        SELECT id, username, used_promo_codes, registration_ip, last_ip
        FROM users
        WHERE used_promo_codes IS NOT NULL
        AND array_length(used_promo_codes, 1) > 0
    LOOP
        -- Collect user's IP addresses (both registration and last IP)
        user_ips := ARRAY[]::TEXT[];

        IF user_record.registration_ip IS NOT NULL AND user_record.registration_ip != 'unknown' THEN
            user_ips := array_append(user_ips, user_record.registration_ip);
        END IF;

        IF user_record.last_ip IS NOT NULL AND user_record.last_ip != 'unknown'
           AND user_record.last_ip != user_record.registration_ip THEN
            user_ips := array_append(user_ips, user_record.last_ip);
        END IF;

        -- Skip if no valid IPs
        IF array_length(user_ips, 1) IS NULL THEN
            CONTINUE;
        END IF;

        -- Loop through each promo code this user has used
        FOREACH promo_code_text IN ARRAY user_record.used_promo_codes
        LOOP
            -- Update the promo_codes table to include these IPs
            UPDATE promo_codes
            SET used_ips = (
                SELECT ARRAY(
                    SELECT DISTINCT unnest(used_ips || user_ips)
                )
            )
            WHERE code = promo_code_text;

            RAISE NOTICE 'Added IPs % for user % to promo code %', user_ips, user_record.username, promo_code_text;
        END LOOP;
    END LOOP;
END $$;

-- Verify the migration results
SELECT
    code,
    current_uses,
    array_length(used_ips, 1) as ip_count,
    used_ips
FROM promo_codes
WHERE array_length(used_ips, 1) > 0
ORDER BY code;
