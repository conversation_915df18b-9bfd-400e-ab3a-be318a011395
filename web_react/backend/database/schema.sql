-- TCF Canada Supabase Database Schema
-- This file contains all the table definitions for migrating from MongoDB to Supabase/PostgreSQL

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHA<PERSON>(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255), -- Legacy column name for compatibility
    password_hash VARCHAR(255), -- New column name used in auth
    membership_type VARCHAR(20) DEFAULT 'free', -- 'free', 'premium', 'lifetime'
    membership_expires_at TIMESTAMPTZ, -- When membership expires (null = lifetime)
    password_reset_token VARCHAR(255),
    password_reset_expires TIMESTAMPTZ,
    used_promo_codes TEXT[], -- Array of promo codes used
    
    -- Additional user profile fields
    supabase_user_id VARCHAR(255), -- For Supabase Auth integration
    first_name <PERSON><PERSON><PERSON><PERSON>(255),
    last_name <PERSON><PERSON><PERSON><PERSON>(255),
    avatar_url TEXT,
    auth_provider VARCHAR(50) DEFAULT 'local', -- 'local', 'google', etc.
    last_login TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Email verification fields (for existing users)
    email_verified BOOLEAN DEFAULT TRUE, -- Changed default to TRUE since new registrations go through pending table
    email_verification_code VARCHAR(10),
    email_verification_expires TIMESTAMPTZ,

    -- IP tracking fields for security and anti-abuse
    registration_ip VARCHAR(45), -- IP address when user registered (supports IPv6)
    last_ip VARCHAR(45), -- Last known IP address

    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Pending registrations table - stores unverified registration attempts
CREATE TABLE IF NOT EXISTS pending_registrations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    verification_code VARCHAR(10) NOT NULL,
    verification_expires TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Ensure unique pending registrations
    UNIQUE(email),
    UNIQUE(username)
);

-- Test questions table - unified storage for all test data
CREATE TABLE IF NOT EXISTS test_questions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    test_type VARCHAR(20) NOT NULL, -- 'reading', 'listening', 'writing', 'speaking'
    test_category VARCHAR(30) NOT NULL, -- 'free', 'premium', 'difficulty', 'month', 'topic'
    test_identifier VARCHAR(50) NOT NULL, -- test1, janvier-2025, Culture, etc.
    question_number VARCHAR(50) NOT NULL, -- Changed from INTEGER to support composite numbers like "17_1"
    question_data JSONB NOT NULL, -- All question-specific data
    free_content JSONB DEFAULT '{}', -- Free content available to all users
    paid_content JSONB DEFAULT '{}', -- Premium content for paid users
    content_flags JSONB DEFAULT '{}', -- Flags indicating content separation
    correct_answer JSONB DEFAULT '{}', -- Correct answers stored separately
    metadata JSONB DEFAULT '{}', -- Additional metadata (difficulty, tags, etc.)
    data_hash VARCHAR(64), -- Hash for detecting changes during sync
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    -- Ensure unique combinations
    UNIQUE(test_type, test_category, test_identifier, question_number)
);

-- Test history table
CREATE TABLE IF NOT EXISTS test_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    test_type VARCHAR(50) NOT NULL, -- 'reading', 'listening', 'writing', 'speaking'
    test_id VARCHAR(50) NOT NULL,
    free BOOLEAN DEFAULT FALSE,
    answers JSONB NOT NULL, -- Store answers as JSON
    score INTEGER,
    max_score INTEGER,
    correct_count INTEGER,
    current_question INTEGER DEFAULT 0, -- Track user's progress through test
    status VARCHAR(20) DEFAULT 'in_progress', -- 'in_progress', 'completed'
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    grading_results JSONB, -- Store detailed grading information
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, test_type, test_id, free)
);

-- Highlights table
CREATE TABLE IF NOT EXISTS highlights (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    section VARCHAR(50) NOT NULL,
    test_id VARCHAR(50) NOT NULL,
    question_index INTEGER NOT NULL,
    free BOOLEAN DEFAULT FALSE,
    highlights JSONB NOT NULL, -- Array of highlight objects with start/end
    highlight_data JSONB, -- Alternative column name used in some API calls
    text_content TEXT, -- Used in some API calls
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, section, test_id, question_index, free)
);

-- Notebook notes table
CREATE TABLE IF NOT EXISTS notebook_notes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    test_path VARCHAR(255) NOT NULL,
    notes TEXT NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, test_path)
);

-- Collection book table (user bookmarks)
CREATE TABLE IF NOT EXISTS collection_book (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    question_id UUID NOT NULL REFERENCES test_questions(id) ON DELETE CASCADE,
    test_type VARCHAR(20) NOT NULL,
    test_category VARCHAR(30) NOT NULL,
    test_identifier VARCHAR(50) NOT NULL,
    question_number VARCHAR(50) NOT NULL,
    free_content JSONB DEFAULT '{}',
    paid_content JSONB DEFAULT '{}',
    content_flags JSONB DEFAULT '{}',
    correct_answer JSONB DEFAULT '{}',
    question_data JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, question_id) -- Prevent duplicate bookmarks
);

-- Promo codes table
CREATE TABLE IF NOT EXISTS promo_codes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(20) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    uses_remaining INTEGER DEFAULT 1, -- Legacy column name
    max_uses INTEGER DEFAULT 1, -- New column name used in scripts
    current_uses INTEGER DEFAULT 0, -- Track how many times used
    membership_days INTEGER DEFAULT 3, -- Legacy column name
    membership_duration INTEGER DEFAULT 3, -- New column name used in scripts
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Promo usage log table
CREATE TABLE IF NOT EXISTS promo_usage_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    username VARCHAR(255),
    promo_code VARCHAR(20) NOT NULL,
    used_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    membership_days INTEGER,
    remaining_uses_after INTEGER
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_membership_type ON users(membership_type);
CREATE INDEX IF NOT EXISTS idx_users_supabase_user_id ON users(supabase_user_id);
CREATE INDEX IF NOT EXISTS idx_users_reset_token ON users(password_reset_token);
CREATE INDEX IF NOT EXISTS idx_users_registration_ip ON users(registration_ip);
CREATE INDEX IF NOT EXISTS idx_users_last_ip ON users(last_ip);

-- Test questions indexes for optimal performance
CREATE INDEX IF NOT EXISTS idx_test_questions_type_category ON test_questions(test_type, test_category);
CREATE INDEX IF NOT EXISTS idx_test_questions_identifier ON test_questions(test_identifier);
CREATE INDEX IF NOT EXISTS idx_test_questions_type_identifier ON test_questions(test_type, test_identifier);
CREATE INDEX IF NOT EXISTS idx_test_questions_data ON test_questions USING GIN(question_data);
CREATE INDEX IF NOT EXISTS idx_test_questions_metadata ON test_questions USING GIN(metadata);
CREATE INDEX IF NOT EXISTS idx_test_questions_hash ON test_questions(data_hash);

CREATE INDEX IF NOT EXISTS idx_test_history_user_id ON test_history(user_id);
CREATE INDEX IF NOT EXISTS idx_test_history_test_type ON test_history(test_type);
CREATE INDEX IF NOT EXISTS idx_test_history_status ON test_history(status);
CREATE INDEX IF NOT EXISTS idx_test_history_created_at ON test_history(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_highlights_user_id ON highlights(user_id);
CREATE INDEX IF NOT EXISTS idx_highlights_section_test ON highlights(section, test_id);

CREATE INDEX IF NOT EXISTS idx_notebook_notes_user_id ON notebook_notes(user_id);
CREATE INDEX IF NOT EXISTS idx_notebook_notes_updated_at ON notebook_notes(updated_at DESC);

CREATE INDEX IF NOT EXISTS idx_collection_book_user_id ON collection_book(user_id);
CREATE INDEX IF NOT EXISTS idx_collection_book_question_id ON collection_book(question_id);
CREATE INDEX IF NOT EXISTS idx_collection_book_test_type ON collection_book(test_type);
CREATE INDEX IF NOT EXISTS idx_collection_book_created_at ON collection_book(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_promo_codes_code ON promo_codes(code);
CREATE INDEX IF NOT EXISTS idx_promo_codes_active ON promo_codes(is_active);

CREATE INDEX IF NOT EXISTS idx_promo_usage_log_user_id ON promo_usage_log(user_id);
CREATE INDEX IF NOT EXISTS idx_promo_usage_log_used_at ON promo_usage_log(used_at DESC);

-- Pending registrations indexes
CREATE INDEX IF NOT EXISTS idx_pending_registrations_email ON pending_registrations(email);
CREATE INDEX IF NOT EXISTS idx_pending_registrations_username ON pending_registrations(username);
CREATE INDEX IF NOT EXISTS idx_pending_registrations_verification_code ON pending_registrations(verification_code);
CREATE INDEX IF NOT EXISTS idx_pending_registrations_expires ON pending_registrations(verification_expires);
CREATE INDEX IF NOT EXISTS idx_pending_registrations_created_at ON pending_registrations(created_at);

-- Row Level Security (RLS) policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE test_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE highlights ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE notebook_notes ENABLE ROW LEVEL SECURITY;  -- Disabled for Flask session auth
ALTER TABLE collection_book ENABLE ROW LEVEL SECURITY;
ALTER TABLE promo_usage_log ENABLE ROW LEVEL SECURITY;

-- Test questions are public (read-only for all users)
-- No RLS needed as this is static content

-- Users can only see/edit their own data
CREATE POLICY "Users can view own profile" ON users
    FOR SELECT USING (auth.uid()::text = id::text);

CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.uid()::text = id::text);

-- Test history policies
CREATE POLICY "Users can view own test history" ON test_history
    FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can insert own test history" ON test_history
    FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);

CREATE POLICY "Users can update own test history" ON test_history
    FOR UPDATE USING (auth.uid()::text = user_id::text);

-- Highlights policies
CREATE POLICY "Users can view own highlights" ON highlights
    FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can insert own highlights" ON highlights
    FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);

CREATE POLICY "Users can update own highlights" ON highlights
    FOR UPDATE USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can delete own highlights" ON highlights
    FOR DELETE USING (auth.uid()::text = user_id::text);

-- Collection book policies
CREATE POLICY "Users can view own bookmarks" ON collection_book
    FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can insert own bookmarks" ON collection_book
    FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);

CREATE POLICY "Users can update own bookmarks" ON collection_book
    FOR UPDATE USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can delete own bookmarks" ON collection_book
    FOR DELETE USING (auth.uid()::text = user_id::text);

-- Notebook notes policies - DISABLED for Flask session-based auth
-- The Flask app handles authorization at the application level
-- CREATE POLICY "Users can view own notebook notes" ON notebook_notes
--     FOR SELECT USING (auth.uid()::text = user_id::text);

-- CREATE POLICY "Users can insert own notebook notes" ON notebook_notes
--     FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);

-- CREATE POLICY "Users can update own notebook notes" ON notebook_notes
--     FOR UPDATE USING (auth.uid()::text = user_id::text);

-- CREATE POLICY "Users can delete own notebook notes" ON notebook_notes
--     FOR DELETE USING (auth.uid()::text = user_id::text);

-- Promo usage log policies
CREATE POLICY "Users can view own promo usage" ON promo_usage_log
    FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can insert own promo usage" ON promo_usage_log
    FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);

-- Promo codes are read-only for regular users
CREATE POLICY "Anyone can view active promo codes" ON promo_codes
    FOR SELECT USING (is_active = true);

-- Functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for automatic timestamp updates
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_test_questions_updated_at BEFORE UPDATE ON test_questions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_highlights_updated_at BEFORE UPDATE ON highlights
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_promo_codes_updated_at BEFORE UPDATE ON promo_codes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to clean up expired pending registrations
CREATE OR REPLACE FUNCTION cleanup_expired_pending_registrations()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM pending_registrations 
    WHERE verification_expires < NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ language 'plpgsql';

-- Sample promo codes for testing
INSERT INTO promo_codes (code, is_active, uses_remaining, membership_days) VALUES
    ('WELCOME3', true, 100, 3),
    ('TESTFREE', true, 50, 7),
    ('PREMIUM30', true, 20, 30)
ON CONFLICT (code) DO NOTHING;

